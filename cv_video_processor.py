"""
Video processing module using OpenCV for MekaVido application.
"""
import os
import cv2
import random
import numpy as np
from typing import List, Tuple, Optional, Dict, Any

class CVVideoProcessor:
    """Class for processing videos using OpenCV."""

    def __init__(self, output_dir="./output"):
        """Initialize the processor with output directory."""
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

        # Check if ffmpeg is available
        self.ffmpeg_available = self._check_ffmpeg()

        # Define available effects
        self.effects = {
            "بدون مؤثرات": self._no_effect,
            "أبيض وأسود": self._black_white_effect,
            "زيادة السطوع": self._brightness_effect,
            "زيادة التباين": self._contrast_effect,
            "عكس الفيديو": self._mirror_effect,
            "تأثير سيبيا": self._sepia_effect,
            "تأثير الحافة": self._edge_effect,
            "تأثير الضباب": self._blur_effect,
            "تأثير الحركة البطيئة": self._slow_motion_effect,
            "تأثير الحركة السريعة": self._fast_motion_effect,
            "تأثير التموج": self._wave_effect,
            "تأثير الدوران": self._rotate_effect,
            "تأثير التلاشي": self._fade_effect
        }

    def _check_ffmpeg(self):
        """Check if ffmpeg is available on the system."""
        try:
            import subprocess
            import shutil

            # First check if ffmpeg is in PATH
            ffmpeg_path = shutil.which('ffmpeg')
            if ffmpeg_path:
                print(f"Found ffmpeg in PATH: {ffmpeg_path}")
                return True

            # Try running ffmpeg directly
            result = subprocess.run(['ffmpeg', '-version'],
                                   stdout=subprocess.PIPE,
                                   stderr=subprocess.PIPE,
                                   text=True,
                                   check=False)
            if result.returncode == 0:
                print("ffmpeg is available in the system")
                return True
            else:
                print("ffmpeg not found in PATH or current directory")
                return False
        except Exception as e:
            print(f"Error checking for ffmpeg: {str(e)}")
            return False

    def get_video_info(self, video_path: str) -> dict:
        """
        Get information about a video file.

        Args:
            video_path (str): Path to the video file

        Returns:
            dict: Dictionary containing video information
        """
        try:
            if not os.path.exists(video_path):
                print(f"Video file does not exist: {video_path}")
                return None

            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"Could not open video file: {video_path}")
                return None

            # Get video properties
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0

            cap.release()

            return {
                'width': width,
                'height': height,
                'fps': fps,
                'frame_count': frame_count,
                'duration': duration
            }

        except Exception as e:
            import traceback
            print(f"Error getting video info: {str(e)}")
            print(traceback.format_exc())
            return None

    def cut_video_into_segments(self, video_path: str, segment_duration: int,
                               num_segments: int, start_minute: int = 0,
                               interval_between_segments: int = 0,
                               sequential_mode: bool = False,
                               save_intermediate: bool = False,
                               direct_portrait: bool = True) -> List[str]:
        """
        Cut a video into multiple segments of specified duration.

        Args:
            video_path (str): Path to the video file
            segment_duration (int): Duration of each segment in seconds
            num_segments (int): Number of segments to create
            start_minute (int): Minute to start cutting from
            interval_between_segments (int): Interval between segments in seconds
            sequential_mode (bool): If True, create sequential segments without gaps
            save_intermediate (bool): If True, save intermediate files
            direct_portrait (bool): If True, convert directly to portrait mode (9:16) while cutting

        Returns:
            List[str]: List of paths to the created segments
        """
        try:
            print(f"Cutting video into segments: {video_path}")
            print(f"Segment duration: {segment_duration} seconds")
            print(f"Number of segments: {num_segments}")
            print(f"Starting from minute: {start_minute}")
            print(f"Interval between segments: {interval_between_segments} seconds")
            print(f"Sequential mode: {sequential_mode}")
            print(f"Direct portrait mode: {direct_portrait}")

            # Check if the video file exists
            if not os.path.exists(video_path):
                print(f"Video file does not exist: {video_path}")
                return []

            # Make sure output directory exists
            os.makedirs(self.output_dir, exist_ok=True)

            # Get video info
            video_info = self.get_video_info(video_path)
            if not video_info:
                print("Failed to get video information")
                return []

            total_duration = video_info['duration']
            fps = video_info['fps']
            width = video_info['width']
            height = video_info['height']

            print(f"Video duration: {total_duration} seconds")
            print(f"Video FPS: {fps}")
            print(f"Video dimensions: {width}x{height}")

            # Convert start_minute to seconds
            start_time_seconds = start_minute * 60

            # Check if start time is valid
            if start_time_seconds >= total_duration:
                print(f"Start time ({start_time_seconds} seconds) exceeds video duration ({total_duration} seconds)")
                return []

            # Calculate remaining duration
            remaining_duration = total_duration - start_time_seconds

            # Calculate how many segments we can create
            if sequential_mode:
                # In sequential mode, we need continuous segments
                total_needed_duration = segment_duration * num_segments
                if remaining_duration < total_needed_duration:
                    max_segments = int(remaining_duration // segment_duration)
                    print(f"Requested {num_segments} segments, but can only create {max_segments} segments")
                    num_segments = max_segments
            else:
                # In interval mode, we need to account for intervals
                total_needed_duration = (segment_duration + interval_between_segments) * num_segments - interval_between_segments
                if remaining_duration < total_needed_duration:
                    max_segments = int((remaining_duration + interval_between_segments) // (segment_duration + interval_between_segments))
                    print(f"Requested {num_segments} segments, but can only create {max_segments} segments")
                    num_segments = max_segments

            if num_segments <= 0:
                print("Cannot create any segments (video too short)")
                return []

            # Generate segment files
            segment_files = []
            final_segments = []

            for i in range(num_segments):
                print(f"Creating segment {i+1}/{num_segments}")

                if sequential_mode:
                    # In sequential mode, segments follow each other without gaps
                    current_start_time = start_time_seconds + (i * segment_duration)
                else:
                    # In interval mode, add interval between segments
                    current_start_time = start_time_seconds + (i * (segment_duration + interval_between_segments))

                current_end_time = current_start_time + segment_duration

                # Check if we've reached the end of the video
                if current_end_time > total_duration:
                    print(f"Reached end of video. Cannot create segment {i+1}")
                    break

                print(f"Cutting segment from {current_start_time} to {current_end_time} seconds")

                # Calculate start and end frames
                start_frame = int(current_start_time * fps)
                end_frame = int(current_end_time * fps)

                # Open the video
                cap = cv2.VideoCapture(video_path)
                if not cap.isOpened():
                    print(f"Could not open video file: {video_path}")
                    continue

                # Set up output video
                output_file = os.path.join(self.output_dir, f"segment_{i+1}.mp4")
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')

                # إذا كان وضع التحويل المباشر إلى عمودي مفعل
                if direct_portrait:
                    # حساب الأبعاد الجديدة للفيديو العمودي (9:16)
                    target_ratio = 9 / 16  # width / height
                    current_ratio = width / height

                    if current_ratio > target_ratio:  # أعرض من 9:16
                        # نحتاج إلى قص العرض
                        new_height = height
                        new_width = int(height * target_ratio)
                        x_offset = (width - new_width) // 2
                        y_offset = 0
                        print(f"Video is wider than 9:16. Cropping width from {width} to {new_width}")
                    else:  # أطول من أو يساوي 9:16
                        # نحتاج إلى قص الارتفاع أو الإبقاء عليه كما هو
                        new_width = width
                        new_height = int(width / target_ratio)
                        x_offset = 0
                        y_offset = (height - new_height) // 2
                        print(f"Video is taller than 9:16. Cropping height from {height} to {new_height}")

                    # التأكد من أن الأبعاد صالحة
                    new_width = max(new_width, 1)
                    new_height = max(new_height, 1)

                    print(f"New dimensions for portrait mode: {new_width}x{new_height}")
                    out = cv2.VideoWriter(output_file, fourcc, fps, (new_width, new_height))
                else:
                    # استخدام الأبعاد الأصلية
                    out = cv2.VideoWriter(output_file, fourcc, fps, (width, height))

                # Set the position to the start frame
                cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)

                # Read and write frames
                frame_count = 0
                while cap.isOpened() and frame_count < (end_frame - start_frame):
                    ret, frame = cap.read()
                    if not ret:
                        break

                    # إذا كان وضع التحويل المباشر إلى عمودي مفعل
                    if direct_portrait:
                        # قص الإطار بناءً على الأبعاد المحسوبة
                        if width / height > 9 / 16:  # أعرض من 9:16
                            # قص العرض
                            x_offset = (width - new_width) // 2
                            frame = frame[:, x_offset:x_offset + new_width]
                        else:  # أطول من أو يساوي 9:16
                            # قص الارتفاع
                            y_offset = (height - new_height) // 2
                            frame = frame[y_offset:y_offset + new_height, :]

                        # تغيير حجم الإطار إذا لزم الأمر
                        if frame.shape[1] != new_width or frame.shape[0] != new_height:
                            frame = cv2.resize(frame, (new_width, new_height))

                    out.write(frame)
                    frame_count += 1

                # Release resources
                cap.release()
                out.release()

                print(f"Saved segment to {output_file}")
                segment_files.append(output_file)

                # If we're not saving intermediate files, we'll only return the final processed segments
                if save_intermediate:
                    final_segments.append(output_file)

            print(f"Created {len(segment_files)} segments")

            # تصحيح المنطق: يجب إرجاع segment_files دائمًا لأنها تحتوي على المقاطع المقطوعة
            # final_segments تستخدم فقط إذا كان save_intermediate مفعلاً
            return segment_files

        except Exception as e:
            import traceback
            print(f"Error cutting video: {str(e)}")
            print(traceback.format_exc())
            return []

    def extract_audio(self, video_path: str, output_name: str = None) -> str:
        """
        Extract audio from a video file.

        Args:
            video_path (str): Path to the video file
            output_name (str, optional): Name for the output audio file

        Returns:
            str: Path to the extracted audio file
        """
        try:
            print(f"Extracting audio from video: {video_path}")

            # Check if the video file exists
            if not os.path.exists(video_path):
                print(f"Video file does not exist: {video_path}")
                return None

            # Generate output file path
            if output_name:
                output_file = os.path.join(self.output_dir, f"{output_name}.mp3")
            else:
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_file = os.path.join(self.output_dir, f"{base_name}_audio.mp3")

            # Make sure output directory exists
            os.makedirs(self.output_dir, exist_ok=True)

            # Use the ffmpeg availability check from initialization
            print(f"ffmpeg availability: {self.ffmpeg_available}")

            if self.ffmpeg_available:
                # Extract audio using ffmpeg
                import subprocess
                cmd = [
                    'ffmpeg',
                    '-i', video_path,
                    '-q:a', '0',
                    '-map', 'a',
                    output_file
                ]

                subprocess.run(cmd,
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE,
                              check=False)

                if os.path.exists(output_file):
                    print(f"Audio extracted successfully: {output_file}")
                    return output_file
                else:
                    print("Failed to extract audio with ffmpeg")

            # Since we can't extract audio without ffmpeg, we'll just return the video path
            # and use it directly in merge_audio_with_video
            print("Audio extraction not supported without ffmpeg. Using video file directly.")
            return video_path

        except Exception as e:
            import traceback
            print(f"Error extracting audio: {str(e)}")
            print(traceback.format_exc())
            return None

    def convert_to_portrait(self, video_path: str, output_name: str = None) -> str:
        """
        Convert a video to portrait mode (9:16 aspect ratio) for Reels/Shorts.

        Args:
            video_path (str): Path to the video file
            output_name (str, optional): Name for the output file

        Returns:
            str: Path to the converted video file
        """
        try:
            print(f"Converting video to portrait mode (9:16): {video_path}")

            # Check if the video file exists
            if not os.path.exists(video_path):
                print(f"Video file does not exist: {video_path}")
                return None

            # Generate output file path
            if output_name:
                output_file = os.path.join(self.output_dir, f"{output_name}.mp4")
            else:
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_file = os.path.join(self.output_dir, f"{base_name}_portrait.mp4")

            # Make sure output directory exists
            os.makedirs(self.output_dir, exist_ok=True)

            # Get video info
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"Could not open video file: {video_path}")
                return None

            # Get video properties
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)

            print(f"Original dimensions: {width}x{height}")

            # Calculate new dimensions for 9:16 aspect ratio (portrait)
            target_ratio = 9 / 16  # width / height
            current_ratio = width / height

            if current_ratio > target_ratio:  # Wider than 9:16
                # Need to crop width
                new_height = height
                new_width = int(height * target_ratio)
                x_offset = (width - new_width) // 2
                y_offset = 0
                print(f"Video is wider than 9:16. Cropping width from {width} to {new_width}")
            else:  # Taller than or equal to 9:16
                # Need to crop height or maintain as is
                new_width = width
                new_height = int(width / target_ratio)
                x_offset = 0
                y_offset = (height - new_height) // 2
                print(f"Video is taller than 9:16. Cropping height from {height} to {new_height}")

            # Ensure we have valid dimensions
            new_width = max(new_width, 1)
            new_height = max(new_height, 1)

            print(f"New dimensions: {new_width}x{new_height}")

            # Set up output video
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_file, fourcc, fps, (new_width, new_height))

            # Process each frame
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break

                # Crop the frame based on calculated offsets
                if current_ratio > target_ratio:  # Wider than 9:16
                    # Crop width
                    frame = frame[:, x_offset:x_offset + new_width]
                else:  # Taller than 9:16
                    # Crop height
                    frame = frame[y_offset:y_offset + new_height, :]

                # Resize if needed to exactly match 9:16
                if frame.shape[1] != new_width or frame.shape[0] != new_height:
                    frame = cv2.resize(frame, (new_width, new_height))

                out.write(frame)

            # Release resources
            cap.release()
            out.release()

            print(f"Converted video saved to: {output_file}")
            return output_file

        except Exception as e:
            import traceback
            print(f"Error converting video to portrait: {str(e)}")
            print(traceback.format_exc())
            return None

    # Effect functions
    def _no_effect(self, frame):
        """No effect, return the original frame."""
        return frame

    def _black_white_effect(self, frame):
        """Convert frame to black and white."""
        return cv2.cvtColor(cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY), cv2.COLOR_GRAY2BGR)

    def _brightness_effect(self, frame, factor=1.5):
        """Increase brightness of the frame."""
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        hsv = np.array(hsv, dtype=np.float64)
        hsv[:,:,2] = hsv[:,:,2] * factor
        hsv[:,:,2][hsv[:,:,2] > 255] = 255
        hsv = np.array(hsv, dtype=np.uint8)
        return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)

    def _contrast_effect(self, frame, factor=1.5):
        """Increase contrast of the frame."""
        lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=factor, tileGridSize=(8, 8))
        cl = clahe.apply(l)
        merged = cv2.merge((cl, a, b))
        return cv2.cvtColor(merged, cv2.COLOR_LAB2BGR)

    def _mirror_effect(self, frame):
        """Mirror the frame horizontally."""
        return cv2.flip(frame, 1)

    def _sepia_effect(self, frame):
        """Apply sepia filter to the frame."""
        sepia_filter = np.array([[0.272, 0.534, 0.131],
                                [0.349, 0.686, 0.168],
                                [0.393, 0.769, 0.189]])
        sepia_img = cv2.transform(frame, sepia_filter)
        sepia_img[np.where(sepia_img > 255)] = 255
        sepia_img = np.array(sepia_img, dtype=np.uint8)
        return sepia_img

    def _edge_effect(self, frame):
        """Apply edge detection to the frame."""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 100, 200)
        return cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)

    def _blur_effect(self, frame, ksize=15):
        """Apply blur to the frame."""
        return cv2.GaussianBlur(frame, (ksize, ksize), 0)

    def _slow_motion_effect(self, frame):
        """Slow motion effect - this is handled differently in apply_effect."""
        # This is just a placeholder, actual implementation is in apply_effect
        return frame

    def _fast_motion_effect(self, frame):
        """Fast motion effect - this is handled differently in apply_effect."""
        # This is just a placeholder, actual implementation is in apply_effect
        return frame

    def _wave_effect(self, frame):
        """Apply wave effect to the frame."""
        rows, cols, _ = frame.shape

        # Create a mapping for the wave effect
        map_x = np.zeros((rows, cols), np.float32)
        map_y = np.zeros((rows, cols), np.float32)

        for i in range(rows):
            for j in range(cols):
                map_x[i, j] = j + 20 * np.sin(i / 10)
                map_y[i, j] = i + 20 * np.cos(j / 10)

        # Apply the mapping
        return cv2.remap(frame, map_x, map_y, cv2.INTER_LINEAR)

    def _rotate_effect(self, frame, angle=15):
        """Rotate the frame by a small angle."""
        rows, cols, _ = frame.shape
        M = cv2.getRotationMatrix2D((cols/2, rows/2), angle, 1)
        return cv2.warpAffine(frame, M, (cols, rows))

    def _fade_effect(self, frame, alpha=0.7):
        """Apply fade effect to the frame."""
        # Create a white frame of the same size
        white_frame = np.ones_like(frame) * 255

        # Blend the original frame with the white frame
        return cv2.addWeighted(frame, alpha, white_frame, 1-alpha, 0)

    def apply_crossfade(self, video1_path: str, video2_path: str, output_name: str = None,
                       fade_duration: int = 30) -> str:
        """
        Apply crossfade transition between two videos.

        Args:
            video1_path (str): Path to the first video
            video2_path (str): Path to the second video
            output_name (str, optional): Name for the output file
            fade_duration (int, optional): Duration of the fade in frames

        Returns:
            str: Path to the output video with crossfade
        """
        try:
            print(f"Applying crossfade between videos:")
            print(f"Video 1: {video1_path}")
            print(f"Video 2: {video2_path}")
            print(f"Fade duration: {fade_duration} frames")

            # Check if the video files exist
            if not os.path.exists(video1_path) or not os.path.exists(video2_path):
                print("One or both video files do not exist")
                return None

            # Generate output file path
            if output_name:
                output_file = os.path.join(self.output_dir, f"{output_name}.mp4")
            else:
                output_file = os.path.join(self.output_dir, "crossfade_output.mp4")

            # Make sure output directory exists
            os.makedirs(self.output_dir, exist_ok=True)

            # Open both videos
            cap1 = cv2.VideoCapture(video1_path)
            cap2 = cv2.VideoCapture(video2_path)

            if not cap1.isOpened() or not cap2.isOpened():
                print("Could not open one or both video files")
                return None

            # Get video properties
            width1 = int(cap1.get(cv2.CAP_PROP_FRAME_WIDTH))
            height1 = int(cap1.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps1 = cap1.get(cv2.CAP_PROP_FPS)
            frame_count1 = int(cap1.get(cv2.CAP_PROP_FRAME_COUNT))

            width2 = int(cap2.get(cv2.CAP_PROP_FRAME_WIDTH))
            height2 = int(cap2.get(cv2.CAP_PROP_FRAME_HEIGHT))
            # fps2 = cap2.get(cv2.CAP_PROP_FPS)  # غير مستخدم، نستخدم fps1 فقط

            # Use properties from the first video
            width, height = width1, height1
            fps = fps1  # استخدام معدل الإطارات من الفيديو الأول

            # Resize second video frames if dimensions don't match
            resize_frames = (width1 != width2 or height1 != height2)

            # Set up output video
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_file, fourcc, fps, (width, height))

            # Write all frames from the first video except the last fade_duration frames
            for i in range(frame_count1 - fade_duration):
                ret, frame = cap1.read()
                if not ret:
                    break
                out.write(frame)

            # Reset first video to start of fade
            cap1.set(cv2.CAP_PROP_POS_FRAMES, frame_count1 - fade_duration)

            # Read first fade_duration frames from second video
            second_video_frames = []
            for i in range(fade_duration):
                ret, frame = cap2.read()
                if not ret:
                    break
                if resize_frames:
                    frame = cv2.resize(frame, (width, height))
                second_video_frames.append(frame)

            # Apply crossfade
            for i in range(fade_duration):
                ret1, frame1 = cap1.read()
                if not ret1:
                    break

                frame2 = second_video_frames[i]

                # Calculate alpha for blending (0 -> 1)
                alpha = i / fade_duration

                # Blend frames
                blended_frame = cv2.addWeighted(frame1, 1 - alpha, frame2, alpha, 0)
                out.write(blended_frame)

            # Write the rest of the second video
            while True:
                ret, frame = cap2.read()
                if not ret:
                    break
                if resize_frames:
                    frame = cv2.resize(frame, (width, height))
                out.write(frame)

            # Release resources
            cap1.release()
            cap2.release()
            out.release()

            print(f"Crossfade applied and saved to: {output_file}")
            return output_file

        except Exception as e:
            import traceback
            print(f"Error applying crossfade: {str(e)}")
            print(traceback.format_exc())
            return None

    def apply_effect(self, video_path: str, effect_name: str, output_name: str = None) -> str:
        """
        Apply an effect to a video.

        Args:
            video_path (str): Path to the video file
            effect_name (str): Name of the effect to apply
            output_name (str, optional): Name for the output file

        Returns:
            str: Path to the processed video file
        """
        try:
            print(f"Applying effect '{effect_name}' to video: {video_path}")

            # Check if the video file exists
            if not os.path.exists(video_path):
                print(f"Video file does not exist: {video_path}")
                return None

            # Check if the effect exists
            if effect_name not in self.effects:
                print(f"Effect '{effect_name}' not found. Using no effect.")
                effect_name = "بدون مؤثرات"

            effect_func = self.effects[effect_name]

            # Generate output file path
            if output_name:
                output_file = os.path.join(self.output_dir, f"{output_name}.mp4")
            else:
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_file = os.path.join(self.output_dir, f"{base_name}_with_effect.mp4")

            # Make sure output directory exists
            os.makedirs(self.output_dir, exist_ok=True)

            # Get video info
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"Could not open video file: {video_path}")
                return None

            # Get video properties
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)

            # Set up output video with adjusted fps for slow/fast motion effects
            output_fps = fps
            if effect_name == "تأثير الحركة البطيئة":
                output_fps = fps / 2  # Half speed
                print(f"Applying slow motion effect: {fps} fps -> {output_fps} fps")
            elif effect_name == "تأثير الحركة السريعة":
                output_fps = fps * 2  # Double speed
                print(f"Applying fast motion effect: {fps} fps -> {output_fps} fps")

            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_file, fourcc, output_fps, (width, height))

            # Process each frame
            frame_count = 0
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # For fast motion, we'll skip frames
            frame_step = 1
            if effect_name == "تأثير الحركة السريعة":
                frame_step = 2  # Process every other frame

            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break

                # For fast motion, skip frames
                if effect_name == "تأثير الحركة السريعة" and frame_count % frame_step != 0:
                    frame_count += 1
                    continue

                # Apply the effect (except for slow/fast motion which is handled by fps change)
                if effect_name not in ["تأثير الحركة البطيئة", "تأثير الحركة السريعة"]:
                    frame = effect_func(frame)

                # For slow motion, duplicate frames
                if effect_name == "تأثير الحركة البطيئة":
                    out.write(frame)  # Write the frame twice

                # Write the frame
                out.write(frame)

                # Update progress
                frame_count += 1
                if frame_count % 100 == 0:
                    progress = (frame_count / total_frames) * 100
                    print(f"Processing: {progress:.1f}% complete")

            # Release resources
            cap.release()
            out.release()

            print(f"Effect applied and saved to: {output_file}")
            return output_file

        except Exception as e:
            import traceback
            print(f"Error applying effect: {str(e)}")
            print(traceback.format_exc())
            return None

    def add_caption_to_video(self, video_path: str, caption_text: str,
                           output_name: str = None,
                           font_style: int = None,
                           font_scale: float = 1.0,
                           font_thickness: int = 2,
                           text_color: tuple = None,
                           bg_color: tuple = None,
                           position: str = "bottom") -> str:
        """
        Add caption text to a video with customizable font options.

        Args:
            video_path (str): Path to the video file
            caption_text (str): Caption text to add
            output_name (str, optional): Name for the output file
            font_style (int, optional): OpenCV font style
            font_scale (float, optional): Font scale
            font_thickness (int, optional): Font thickness
            text_color (tuple, optional): Text color in BGR format
            bg_color (tuple, optional): Background color in BGR format
            position (str, optional): Text position (top, middle, bottom)

        Returns:
            str: Path to the video with caption
        """
        try:
            print(f"Adding caption to video: {video_path}")
            print(f"Caption text: {caption_text}")

            # Check if the video file exists
            if not os.path.exists(video_path):
                print(f"Video file does not exist: {video_path}")
                return None

            # Generate output file path
            if output_name:
                output_file = os.path.join(self.output_dir, f"{output_name}.mp4")
            else:
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_file = os.path.join(self.output_dir, f"{base_name}_with_caption.mp4")

            # Make sure output directory exists
            os.makedirs(self.output_dir, exist_ok=True)

            # Get video info
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"Could not open video file: {video_path}")
                return None

            # Get video properties
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)

            # Set up output video
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_file, fourcc, fps, (width, height))

            # Set up caption text properties with defaults if not provided
            if font_style is None:
                font_style = cv2.FONT_HERSHEY_SIMPLEX
            if text_color is None:
                text_color = (255, 255, 255)  # White
            if bg_color is None:
                bg_color = (0, 0, 0)  # Black

            # Available font styles in OpenCV
            font_styles = {
                "simplex": cv2.FONT_HERSHEY_SIMPLEX,
                "plain": cv2.FONT_HERSHEY_PLAIN,
                "duplex": cv2.FONT_HERSHEY_DUPLEX,
                "complex": cv2.FONT_HERSHEY_COMPLEX,
                "triplex": cv2.FONT_HERSHEY_TRIPLEX,
                "complex_small": cv2.FONT_HERSHEY_COMPLEX_SMALL,
                "script_simplex": cv2.FONT_HERSHEY_SCRIPT_SIMPLEX,
                "script_complex": cv2.FONT_HERSHEY_SCRIPT_COMPLEX
            }

            # If font_style is a string, convert to OpenCV constant
            if isinstance(font_style, str) and font_style.lower() in font_styles:
                font_style = font_styles[font_style.lower()]

            # Split text into lines if it's too long
            max_chars_per_line = 40  # Adjust based on your needs
            words = caption_text.split()
            lines = []
            current_line = ""

            for word in words:
                if len(current_line) + len(word) + 1 <= max_chars_per_line:
                    if current_line:
                        current_line += " " + word
                    else:
                        current_line = word
                else:
                    lines.append(current_line)
                    current_line = word

            if current_line:
                lines.append(current_line)

            # Calculate text sizes for positioning
            text_sizes = [cv2.getTextSize(line, font_style, font_scale, font_thickness)[0] for line in lines]
            max_text_width = max(size[0] for size in text_sizes)
            total_text_height = sum(size[1] for size in text_sizes) + (len(lines) - 1) * 10  # 10px spacing

            # Determine vertical position
            if position.lower() == "top":
                text_y_start = 50
            elif position.lower() == "middle":
                text_y_start = (height - total_text_height) // 2
            else:  # bottom
                text_y_start = height - total_text_height - 50

            # Process each frame
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break

                # Create a semi-transparent background for better readability
                overlay = frame.copy()

                # Calculate background rectangle dimensions
                bg_x1 = (width - max_text_width) // 2 - 20
                bg_y1 = text_y_start - 20
                bg_x2 = bg_x1 + max_text_width + 40
                bg_y2 = bg_y1 + total_text_height + 40

                # Draw background rectangle
                cv2.rectangle(overlay,
                             (bg_x1, bg_y1),
                             (bg_x2, bg_y2),
                             bg_color, -1)

                # Apply transparency
                alpha = 0.7
                frame = cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0)

                # Add each line of text
                current_y = text_y_start
                for i, line in enumerate(lines):
                    text_size = text_sizes[i]
                    text_x = (width - text_size[0]) // 2  # Center horizontally

                    # Add the caption text
                    cv2.putText(frame, line, (text_x, current_y), font_style,
                               font_scale, text_color, font_thickness)

                    current_y += text_size[1] + 10  # Move to next line with spacing

                out.write(frame)

            # Release resources
            cap.release()
            out.release()

            print(f"Caption added and saved to: {output_file}")
            return output_file

        except Exception as e:
            import traceback
            print(f"Error adding caption to video: {str(e)}")
            print(traceback.format_exc())
            return None

    def merge_audio_with_video(self, video_path: str, audio_path: str,
                              output_name: str = None, volume: float = 1.0,
                              audio_start_time: float = 0.0) -> str:
        """
        Merge audio with a video file.

        Args:
            video_path (str): Path to the video file
            audio_path (str): Path to the audio file
            output_name (str, optional): Name for the output file
            volume (float, optional): Volume level for the audio (1.0 = 100%)
            audio_start_time (float, optional): Start time in seconds for the audio (0.0 = start from beginning)

        Returns:
            str: Path to the merged video file
        """
        try:
            print(f"Merging audio with video:")
            print(f"Video: {video_path}")
            print(f"Audio: {audio_path}")

            # Check if the files exist
            if not os.path.exists(video_path):
                print(f"Video file does not exist: {video_path}")
                return None

            if not os.path.exists(audio_path):
                print(f"Audio file does not exist: {audio_path}")
                return None

            # Generate output file path
            if output_name:
                output_file = os.path.join(self.output_dir, f"{output_name}.mp4")
            else:
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_file = os.path.join(self.output_dir, f"{base_name}_with_audio.mp4")

            # Make sure output directory exists
            os.makedirs(self.output_dir, exist_ok=True)

            # Use the ffmpeg availability check from initialization
            print(f"ffmpeg availability: {self.ffmpeg_available}")

            if self.ffmpeg_available:
                # Merge audio with video using ffmpeg
                import subprocess
                # تطبيق مستوى الصوت ووقت بدء الصوت
                print(f"Applying audio volume: {volume*100}%")
                print(f"Audio start time: {audio_start_time} seconds")

                # إنشاء أمر ffmpeg
                cmd = ['ffmpeg', '-y', '-i', video_path]  # إضافة -y لتجاوز الملفات الموجودة

                # إضافة وقت بدء الصوت إذا كان أكبر من صفر
                if audio_start_time > 0:
                    cmd.extend(['-ss', str(audio_start_time), '-i', audio_path])
                else:
                    cmd.extend(['-i', audio_path])

                # إضافة باقي الخيارات
                cmd.extend([
                    '-c:v', 'copy',  # نسخ الفيديو بدون إعادة ترميز
                    '-c:a', 'aac',   # ترميز الصوت بصيغة AAC
                    '-b:a', '192k',  # معدل بت الصوت
                    '-filter:a', f'volume={volume}',  # تعديل مستوى الصوت
                    '-map', '0:v:0',  # استخدام مسار الفيديو من الملف الأول
                    '-map', '1:a:0',  # استخدام مسار الصوت من الملف الثاني
                    '-shortest',      # استخدام أقصر مدة
                    '-strict', 'experimental',  # السماح بالخيارات التجريبية
                    output_file
                ])

                # تنفيذ الأمر بدون طباعة الأمر الكامل (لتجنب مشاكل الترميز مع المسارات العربية)
                print(f"تنفيذ أمر ffmpeg لدمج الصوت مع الفيديو...")

                # تعديل الأمر لاستخدام ملف مؤقت للأمر
                try:
                    # استخدام subprocess.Popen بدلاً من subprocess.run
                    # وتجنب استخدام stdout و stderr لتجنب مشاكل الترميز
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.DEVNULL,  # تجاهل المخرجات
                        stderr=subprocess.DEVNULL,  # تجاهل الأخطاء
                        shell=False
                    )

                    # انتظار انتهاء العملية
                    process.wait()

                    # التحقق من رمز الخروج
                    if process.returncode != 0:
                        print(f"تحذير: انتهى ffmpeg برمز خروج {process.returncode}")
                    else:
                        print("تم تنفيذ أمر ffmpeg بنجاح")

                except Exception as run_error:
                    print(f"خطأ في تنفيذ أمر ffmpeg: {str(run_error)}")

                # التحقق من نجاح العملية
                if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                    print(f"تم دمج الصوت بنجاح: {output_file}")

                    # التحقق من وجود مسار صوت في الملف الناتج
                    check_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'a',
                                '-show_entries', 'stream=codec_type', '-of', 'default=noprint_wrappers=1',
                                output_file]
                    try:
                        check_result = subprocess.run(check_cmd,
                                                    stdout=subprocess.PIPE,
                                                    stderr=subprocess.PIPE,
                                                    text=True,
                                                    check=False)
                        if 'codec_type=audio' in check_result.stdout:
                            print("تم التحقق من وجود مسار صوت في الملف الناتج")
                        else:
                            print("تحذير: لم يتم العثور على مسار صوت في الملف الناتج!")
                    except Exception as probe_error:
                        print(f"خطأ في التحقق من مسار الصوت: {str(probe_error)}")

                    return output_file
                else:
                    print("فشل في دمج الصوت باستخدام ffmpeg")

            # Since we can't merge audio without ffmpeg, we'll just copy the video
            import shutil
            try:
                shutil.copy2(video_path, output_file)
                print(f"Audio merging not supported without ffmpeg. Copied video file to {output_file}")
                return output_file
            except Exception as copy_error:
                print(f"Error copying video file: {str(copy_error)}")
                return video_path

        except Exception as e:
            import traceback
            print(f"Error merging audio with video: {str(e)}")
            print(traceback.format_exc())
            return None
