"""
Speech recognition module for MekaVido application.
"""
import os
import tempfile
import traceback
import subprocess

# محاولة استيراد مكتبة speech_recognition
try:
    import speech_recognition as sr
    SR_AVAILABLE = True
    print("تم تحميل مكتبة speech_recognition بنجاح")
except ImportError:
    SR_AVAILABLE = False
    print("مكتبة speech_recognition غير متوفرة")

# محاولة استيراد مكتبة pydub
try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
    print("تم تحميل مكتبة pydub بنجاح")
except ImportError:
    PYDUB_AVAILABLE = False
    print("مكتبة pydub غير متوفرة")

class SpeechRecognizer:
    """Class for recognizing speech from audio files."""

    def __init__(self, output_dir="output"):
        """
        Initialize the speech recognizer.

        Args:
            output_dir (str): Directory to save output files
        """
        self.output_dir = output_dir

        # تهيئة المتعرف على الكلام إذا كانت المكتبة متوفرة
        if SR_AVAILABLE:
            self.recognizer = sr.Recognizer()
        else:
            self.recognizer = None
            print("تحذير: مكتبة speech_recognition غير متوفرة، لن يعمل التعرف على الكلام")

    def check_ffmpeg(self):
        """
        التحقق من وجود ffmpeg على النظام.

        Returns:
            bool: True إذا كان ffmpeg متوفرًا، False خلاف ذلك
        """
        try:
            result = subprocess.run(['ffmpeg', '-version'],
                                   stdout=subprocess.PIPE,
                                   stderr=subprocess.PIPE,
                                   text=True,
                                   check=False)
            return result.returncode == 0
        except:
            return False

    def convert_to_wav(self, audio_path):
        """
        Convert audio file to WAV format for speech recognition.

        Args:
            audio_path (str): Path to the audio file

        Returns:
            str: Path to the converted WAV file
        """
        try:
            # التحقق من وجود pydub
            if not PYDUB_AVAILABLE:
                print("مكتبة pydub غير متوفرة، لا يمكن تحويل الصوت")
                return None

            # إنشاء ملف مؤقت
            temp_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
            temp_path = temp_file.name
            temp_file.close()

            # الحصول على امتداد الملف
            _, ext = os.path.splitext(audio_path)
            ext = ext.lower()

            # تحويل إلى WAV
            if ext == ".mp3":
                audio = AudioSegment.from_mp3(audio_path)
            elif ext == ".m4a":
                audio = AudioSegment.from_file(audio_path, format="m4a")
            elif ext == ".wav":
                # الملف بالفعل بصيغة WAV، مجرد نسخه
                import shutil
                shutil.copy2(audio_path, temp_path)
                return temp_path
            else:
                # محاولة تحميل الملف باستخدام from_file العام
                audio = AudioSegment.from_file(audio_path)

            # تصدير بصيغة WAV
            audio.export(temp_path, format="wav")
            return temp_path

        except Exception as e:
            print(f"خطأ في تحويل الصوت إلى WAV: {str(e)}")
            print(traceback.format_exc())
            return None

    def recognize_speech(self, audio_path, language="ar"):
        """
        Recognize speech from an audio file.

        Args:
            audio_path (str): Path to the audio file
            language (str): Language code for recognition

        Returns:
            str: Recognized text
        """
        try:
            print(f"التعرف على الكلام من: {audio_path}")

            # التحقق من وجود الملف
            if not os.path.exists(audio_path):
                print(f"ملف الصوت غير موجود: {audio_path}")
                return None

            # التحقق من توفر المكتبات اللازمة
            if not SR_AVAILABLE:
                fallback_text = "مكتبة speech_recognition غير متوفرة. يرجى تثبيتها باستخدام: pip install SpeechRecognition"
                print(fallback_text)
                return fallback_text

            if not PYDUB_AVAILABLE:
                fallback_text = "مكتبة pydub غير متوفرة. يرجى تثبيتها باستخدام: pip install pydub"
                print(fallback_text)
                return fallback_text

            # التحقق من وجود ffmpeg
            if not self.check_ffmpeg():
                fallback_text = "برنامج ffmpeg غير متوفر. يرجى تثبيته لتمكين التعرف على الكلام."
                print(fallback_text)
                return fallback_text

            # تحويل إلى WAV إذا لزم الأمر
            wav_path = self.convert_to_wav(audio_path)
            if not wav_path:
                print("فشل في تحويل الصوت إلى صيغة WAV")
                return None

            # التعرف على الكلام
            with sr.AudioFile(wav_path) as source:
                audio_data = self.recognizer.record(source)

                # محاولة التعرف باستخدام Google API
                try:
                    text = self.recognizer.recognize_google(audio_data, language=language)
                    print(f"النص المتعرف عليه: {text}")
                    return text
                except sr.UnknownValueError:
                    print("لم يتمكن Google Speech Recognition من فهم الصوت")
                    return "لم يتمكن النظام من فهم الصوت"
                except sr.RequestError as e:
                    print(f"تعذر طلب النتائج من خدمة Google Speech Recognition؛ {e}")
                    return f"تعذر الاتصال بخدمة التعرف على الكلام: {e}"
                finally:
                    # تنظيف الملف المؤقت
                    try:
                        os.remove(wav_path)
                    except:
                        pass

        except Exception as e:
            print(f"خطأ في التعرف على الكلام: {str(e)}")
            print(traceback.format_exc())
            return f"حدث خطأ أثناء التعرف على الكلام: {str(e)}"
