"""
Video download utilities for MekaVido application.
"""
import os
import yt_dlp
from pytube import YouTube

class VideoDownloader:
    """Class for downloading videos from YouTube."""

    def __init__(self, output_path="./downloads"):
        """Initialize the downloader with output path."""
        self.output_path = output_path
        os.makedirs(output_path, exist_ok=True)

    def download_with_pytube(self, url, resolution='720p'):
        """
        Download video using pytube.

        Args:
            url (str): YouTube video URL
            resolution (str): Video resolution

        Returns:
            str: Path to downloaded video file
        """
        try:
            yt = YouTube(url)
            video = yt.streams.filter(progressive=True, file_extension='mp4',
                                     resolution=resolution).first()

            if not video:
                # Try to get the highest resolution available
                video = yt.streams.filter(progressive=True,
                                         file_extension='mp4').order_by('resolution').desc().first()

            if not video:
                return None, "No suitable video stream found"

            file_path = video.download(output_path=self.output_path)
            return file_path, None

        except Exception as e:
            return None, str(e)

    def download_with_ytdlp(self, url, quality='high'):
        """
        Download video using yt-dlp.

        Args:
            url (str): YouTube video URL
            quality (str): Quality of the video ('high', 'medium', 'low')

        Returns:
            str: Path to downloaded video file
        """
        try:
            print(f"Downloading video from {url} using yt-dlp")
            print(f"Output path: {self.output_path}")
            print(f"Requested quality: {quality}")

            # Make sure output directory exists
            os.makedirs(self.output_path, exist_ok=True)

            # Set format based on quality
            if quality == 'high':
                format_spec = 'bestvideo[height<=1080]+bestaudio/best[height<=1080]'
            elif quality == 'medium':
                format_spec = 'bestvideo[height<=720]+bestaudio/best[height<=720]'
            elif quality == 'low':
                format_spec = 'bestvideo[height<=480]+bestaudio/best[height<=480]'
            else:
                format_spec = 'best'

            print(f"Using format specification: {format_spec}")

            ydl_opts = {
                'format': format_spec,
                'outtmpl': os.path.join(self.output_path, '%(title)s.%(ext)s'),
                'quiet': False,  # Set to False to see download progress
                'no_warnings': False,
                'ignoreerrors': False,
                'merge_output_format': 'mp4',  # Force output to be mp4
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                print("Starting download...")
                info = ydl.extract_info(url, download=True)
                if info is None:
                    return None, "Failed to extract video information"

                file_path = ydl.prepare_filename(info)
                print(f"Download completed: {file_path}")

                # Check if file exists
                if not os.path.exists(file_path):
                    # Try to find the file with a different extension
                    base_path = os.path.splitext(file_path)[0]
                    for ext in ['.mp4', '.webm', '.mkv']:
                        test_path = base_path + ext
                        if os.path.exists(test_path):
                            file_path = test_path
                            break

                if os.path.exists(file_path):
                    return file_path, None
                else:
                    return None, f"Downloaded file not found at {file_path}"

        except Exception as e:
            import traceback
            print(f"Error downloading video: {str(e)}")
            print(traceback.format_exc())
            return None, str(e)

    def download_audio_only(self, url, quality='high'):
        """
        Download only the audio from a video.

        Args:
            url (str): YouTube video URL
            quality (str): Quality of the audio ('high', 'medium', 'low')

        Returns:
            str: Path to downloaded audio file
        """
        try:
            print(f"Downloading audio from {url}")
            print(f"Output path: {self.output_path}")
            print(f"Requested quality: {quality}")

            # Make sure output directory exists
            os.makedirs(self.output_path, exist_ok=True)

            # Set quality based on parameter
            if quality == 'high':
                audio_quality = '320'
            elif quality == 'medium':
                audio_quality = '192'
            else:  # low
                audio_quality = '128'

            print(f"Using audio quality: {audio_quality}k")

            # Check if ffmpeg is available
            try:
                import subprocess
                result = subprocess.run(['ffmpeg', '-version'],
                                       stdout=subprocess.PIPE,
                                       stderr=subprocess.PIPE,
                                       text=True,
                                       check=False)
                ffmpeg_available = result.returncode == 0
            except:
                ffmpeg_available = False

            # Configure yt-dlp options
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': os.path.join(self.output_path, '%(title)s.%(ext)s'),
                'quiet': False,  # Set to False to see download progress
                'no_warnings': False,
            }

            # Add post-processors if ffmpeg is available
            if ffmpeg_available:
                ydl_opts['postprocessors'] = [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': audio_quality,
                }]
                print("FFmpeg available, will convert to MP3")
            else:
                print("FFmpeg not available, downloading best audio format without conversion")

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                print("Starting audio download...")
                info = ydl.extract_info(url, download=True)

                if info is None:
                    return None, "Failed to extract audio information"

                if ffmpeg_available:
                    # If ffmpeg is available, the file will be converted to mp3
                    file_path = os.path.splitext(ydl.prepare_filename(info))[0] + '.mp3'
                else:
                    # Otherwise, use the original file
                    file_path = ydl.prepare_filename(info)

                print(f"Audio download completed: {file_path}")

                # Check if file exists
                if not os.path.exists(file_path):
                    # Try to find the file with a different extension
                    base_path = os.path.splitext(file_path)[0]
                    for ext in ['.mp3', '.m4a', '.webm', '.opus']:
                        test_path = base_path + ext
                        if os.path.exists(test_path):
                            file_path = test_path
                            break

                if os.path.exists(file_path):
                    return file_path, None
                else:
                    return None, f"Downloaded audio file not found at {file_path}"

        except Exception as e:
            import traceback
            print(f"Error downloading audio: {str(e)}")
            print(traceback.format_exc())
            return None, str(e)
