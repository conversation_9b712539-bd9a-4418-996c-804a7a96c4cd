"""
Main window UI for MekaVido application.
"""
import os
import sys

from PyQt5.QtWidgets import (QMain<PERSON>indow, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QLineEdit, QPushButton, QSpinBox,
                            QProgressBar, QFileDialog, QCheckBox, QGroupBox,
                            QComboBox, QMessageBox, QListWidget, QListWidgetItem, QSlider,
                            QColorDialog, QFrame, QGridLayout, QScrollArea, QSizePolicy)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QUrl, QSize
from PyQt5.QtGui import QIcon, QPixmap, QColor
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent

# Import our modules will be done in the VideoProcessingThread class

class VideoProcessingThread(QThread):
    """Thread for video processing operations."""
    progress_updated = pyqtSignal(int)
    processing_completed = pyqtSignal(bool, str, list)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.task = None
        self.params = None

        # Initialize paths
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.output_dir = os.path.join(self.base_dir, "output")
        os.makedirs(self.output_dir, exist_ok=True)

        # Store downloaded files
        self.video_file = None
        self.audio_file = None
        self.generated_files = []

        # Import processors here to avoid circular imports
        import sys
        sys.path.append(self.base_dir)

        from utils.download import VideoDownloader
        from cv_video_processor import CVVideoProcessor
        from speech_recognition_module import SpeechRecognizer

        self.downloader = VideoDownloader(self.output_dir)
        self.video_processor = CVVideoProcessor(self.output_dir)
        self.speech_recognizer = SpeechRecognizer(self.output_dir)

        # Store recognized text
        self.recognized_text = None

    def setup_task(self, task, params):
        """Set up the task and parameters."""
        self.task = task
        self.params = params

        # إذا كانت المهمة هي توليد الفيديوهات، تأكد من تعيين ملف الفيديو
        if task == 'generate' and 'video_file' in params:
            self.video_file = params['video_file']
            print(f"تم تعيين ملف الفيديو من المعلمات: {self.video_file}")

            # التحقق من وجود الملف
            if self.video_file and os.path.exists(self.video_file):
                print(f"ملف الفيديو موجود: {self.video_file}")
                print(f"حجم الملف: {os.path.getsize(self.video_file) / (1024*1024):.2f} ميجابايت")
            else:
                print(f"تحذير: ملف الفيديو غير موجود: {self.video_file}")

    def run(self):
        """Run the task."""
        try:
            if self.task == 'download':
                self._download_videos()
            elif self.task == 'generate':
                self._generate_videos()
            elif self.task == 'recognize_speech':
                self._recognize_speech()
        except Exception as e:
            import traceback
            print(f"Error in processing thread: {str(e)}")
            print(traceback.format_exc())
            self.processing_completed.emit(False, str(e), [])

    def _recognize_speech(self):
        """Recognize speech from the audio file."""
        if not self.audio_file:
            self.processing_completed.emit(False, "لم يتم تحميل ملف صوتي", [])
            return

        try:
            print(f"Recognizing speech from audio file: {self.audio_file}")
            self.progress_updated.emit(10)

            # Recognize speech
            language = self.params.get('language', 'ar-AR')
            self.recognized_text = self.speech_recognizer.recognize_speech(
                self.audio_file, language=language
            )

            self.progress_updated.emit(100)

            if self.recognized_text:
                self.processing_completed.emit(
                    True,
                    "تم التعرف على الكلام بنجاح",
                    [self.recognized_text]
                )
            else:
                self.processing_completed.emit(
                    False,
                    "فشل في التعرف على الكلام",
                    []
                )

        except Exception as e:
            import traceback
            print(f"Error recognizing speech: {str(e)}")
            print(traceback.format_exc())
            self.processing_completed.emit(False, str(e), [])

    def _download_videos(self):
        """Download videos from the provided URLs."""
        video_url = self.params.get('video_url')
        audio_url = self.params.get('audio_url')
        quality = self.params.get('quality', 'high')
        audio_download_type = self.params.get('audio_download_type', 'extract_from_video')

        try:
            progress = 0

            # Download main video if URL is provided
            if video_url:
                print(f"Downloading main video from: {video_url}")
                print(f"Selected quality: {quality}")
                self.progress_updated.emit(progress + 10)

                video_file, error = self.downloader.download_with_ytdlp(video_url, quality)
                if error:
                    raise Exception(f"Error downloading main video: {error}")

                self.video_file = video_file
                progress = 50
                self.progress_updated.emit(progress)

            # Download audio if URL is provided
            if audio_url:
                print(f"Processing audio from: {audio_url}")

                if audio_download_type == 'audio_only':
                    # Download audio directly
                    print("Downloading audio only")
                    audio_file, error = self.downloader.download_audio_only(audio_url, quality)
                    if error:
                        raise Exception(f"Error downloading audio: {error}")

                    self.audio_file = audio_file
                else:
                    # Download video and extract audio
                    print("Downloading video and extracting audio")
                    audio_video_file, error = self.downloader.download_with_ytdlp(audio_url, quality)
                    if error:
                        raise Exception(f"Error downloading audio video: {error}")

                    # Extract audio from the audio video
                    self.audio_file = self.video_processor.extract_audio(audio_video_file)

                progress = 100
                self.progress_updated.emit(progress)

            # If we have at least one file, consider it a success
            if self.video_file or self.audio_file:
                self.processing_completed.emit(True, "تم تحميل الملفات بنجاح", [self.video_file, self.audio_file])
            else:
                self.processing_completed.emit(False, "لم يتم تحميل أي ملفات", [])

        except Exception as e:
            import traceback
            print(f"Error downloading media: {str(e)}")
            print(traceback.format_exc())
            self.processing_completed.emit(False, str(e), [])

    def _generate_videos(self):
        """Generate video segments with the specified settings."""
        # التحقق من وجود ملف الفيديو الأساسي
        if not self.video_file:
            error_message = "لم يتم تحميل الفيديو الأساسي"
            print(error_message)
            print("قيمة self.video_file: None")

            # التحقق من وجود ملف فيديو في المعلمات
            if 'video_file' in self.params and self.params['video_file']:
                self.video_file = self.params['video_file']
                print(f"تم العثور على ملف فيديو في المعلمات: {self.video_file}")
            else:
                print("لم يتم العثور على ملف فيديو في المعلمات")
                self.processing_completed.emit(False, error_message, [])
                return

        # التحقق من وجود الملف على القرص
        if not os.path.exists(self.video_file):
            error_message = f"الملف الأساسي غير موجود: {self.video_file}"
            print(error_message)
            self.processing_completed.emit(False, error_message, [])
            return

        # التحقق من أن الملف هو فيديو
        try:
            # طباعة معلومات تشخيصية
            print(f"التحقق من ملف الفيديو: {self.video_file}")
            print(f"حجم الملف: {os.path.getsize(self.video_file) / (1024*1024):.2f} ميجابايت")
            print(f"امتداد الملف: {os.path.splitext(self.video_file)[1]}")

            # التحقق من صلاحية الملف باستخدام OpenCV
            import cv2
            cap = cv2.VideoCapture(self.video_file)
            if not cap.isOpened():
                error_message = f"لا يمكن فتح ملف الفيديو: {self.video_file}"
                print(error_message)
                self.processing_completed.emit(False, error_message, [])
                cap.release()
                return

            # التحقق من مدة الفيديو
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            print(f"معلومات الفيديو: {width}x{height}, {fps} fps, {frame_count} إطار")

            if fps <= 0 or frame_count <= 0:
                error_message = f"ملف الفيديو فارغ أو تالف: {self.video_file}"
                print(error_message)
                self.processing_completed.emit(False, error_message, [])
                cap.release()
                return

            # التحقق من قراءة إطار واحد على الأقل
            ret, frame = cap.read()
            if not ret or frame is None:
                error_message = f"لا يمكن قراءة إطارات من ملف الفيديو: {self.video_file}"
                print(error_message)
                self.processing_completed.emit(False, error_message, [])
                cap.release()
                return

            # الفيديو صالح
            print(f"تم التحقق من صلاحية الفيديو: {self.video_file}")
            cap.release()

        except Exception as e:
            import traceback
            error_message = f"خطأ في التحقق من ملف الفيديو: {str(e)}"
            print(error_message)
            print(traceback.format_exc())
            self.processing_completed.emit(False, error_message, [])
            return

        try:
            # Get basic settings
            duration = self.params.get('duration', 15)
            count = self.params.get('count', 5)
            effect = self.params.get('effect', "بدون مؤثرات")
            generate_captions = self.params.get('generate_captions', True)
            portrait_mode = self.params.get('portrait_mode', True)  # 9:16 format for Reels/Shorts
            caption_text = self.params.get('caption_text', "MekaVido")  # Default caption text

            # Get advanced cutting settings
            start_minute = self.params.get('start_minute', 0)
            interval_between_segments = self.params.get('interval_between_segments', 0)
            sequential_mode = self.params.get('sequential_mode', False)
            save_intermediate = self.params.get('save_intermediate', False)

            # Step 1: Cut video into segments
            print(f"المرحلة 1: تقطيع الفيديو إلى {count} مقاطع بمدة {duration} ثانية لكل مقطع")
            print(f"البدء من الدقيقة: {start_minute}")
            print(f"الفاصل الزمني بين المقاطع: {interval_between_segments} ثانية")
            print(f"الوضع المتسلسل: {sequential_mode}")
            self.progress_updated.emit(10)

            # تمرير معلمة direct_portrait لتقطيع الفيديو مباشرة إلى مقاس الريلز
            segments = self.video_processor.cut_video_into_segments(
                self.video_file, duration, count,
                start_minute=start_minute,
                interval_between_segments=interval_between_segments,
                sequential_mode=sequential_mode,
                save_intermediate=save_intermediate,
                direct_portrait=portrait_mode  # استخدام قيمة portrait_mode لتحديد ما إذا كان سيتم التحويل المباشر
            )

            if not segments:
                self.processing_completed.emit(False, "فشل في تقطيع الفيديو إلى مقاطع", [])
                return

            print(f"تم إنشاء {len(segments)} مقطع بنجاح")

            # التحقق من عدد المقاطع المنشأة
            if len(segments) < count:
                print(f"تحذير: تم طلب {count} مقاطع، ولكن تم إنشاء {len(segments)} مقطع فقط (قد يكون الفيديو قصيرًا جدًا)")
            elif len(segments) != count:
                print(f"تحذير: عدد المقاطع المنشأة ({len(segments)}) لا يتطابق مع العدد المطلوب ({count})")

            self.progress_updated.emit(30)

            # Step 2: Process each segment
            final_segments = []
            intermediate_files = []  # لتتبع جميع الملفات الوسيطة

            for i, segment in enumerate(segments):
                print(f"Processing segment {i+1}/{len(segments)}")
                current_segment = segment
                intermediate_files.append(segment)  # إضافة ملف القطع الأصلي إلى الملفات الوسيطة

                # تطبيق المراحل بشكل تسلسلي على نفس الملف

                # المرحلة 1: تحويل إلى وضع عمودي إذا لم يتم ذلك بالفعل
                if portrait_mode and not self.params.get('direct_portrait', True):
                    # تحويل إلى وضع عمودي فقط إذا لم يتم التحويل المباشر أثناء التقطيع
                    print("المرحلة 1: تحويل إلى وضع عمودي (9:16)")
                    portrait_segment = self.video_processor.convert_to_portrait(
                        current_segment, f"segment_{i+1}_step1_portrait"
                    )
                    if portrait_segment:
                        current_segment = portrait_segment
                        intermediate_files.append(portrait_segment)
                elif portrait_mode:
                    print("تخطي تحويل الوضع العمودي (تم بالفعل أثناء القطع)")

                # المرحلة 2: تطبيق المؤثر المحدد
                if effect != "بدون مؤثرات":
                    print(f"المرحلة 2: تطبيق المؤثر: {effect}")
                    effect_segment = self.video_processor.apply_effect(
                        current_segment, effect, f"segment_{i+1}_step2_effect"
                    )
                    if effect_segment:
                        current_segment = effect_segment
                        intermediate_files.append(effect_segment)

                # المرحلة 3: إضافة الصوت
                audio_file_to_use = None
                use_audio = self.params.get('use_audio', True)
                custom_audio_file = self.params.get('audio_file', None)
                audio_volume = self.params.get('audio_volume', 1.0)

                # حساب وقت بدء الصوت لكل مقطع
                start_minute = self.params.get('start_minute', 0)
                audio_start_minute = self.params.get('audio_start_minute', start_minute)  # استخدام نقطة بداية الصوت المحددة
                duration = self.params.get('duration', 15)
                interval_between_segments = self.params.get('interval_between_segments', 0)
                sequential_mode = self.params.get('sequential_mode', False)

                # حساب وقت بدء الصوت بناءً على رقم المقطع
                audio_start_time = 0.0
                if sequential_mode:
                    # في الوضع المتسلسل، كل مقطع يبدأ من حيث انتهى المقطع السابق
                    audio_start_time = audio_start_minute * 60 + i * duration
                else:
                    # في الوضع العادي، كل مقطع يبدأ بعد فاصل زمني من المقطع السابق
                    audio_start_time = audio_start_minute * 60 + i * (duration + interval_between_segments)

                print(f"وقت بدء الصوت للمقطع {i+1}: {audio_start_time} ثانية")

                if use_audio:
                    if custom_audio_file and os.path.exists(custom_audio_file):
                        audio_file_to_use = custom_audio_file
                        print(f"استخدام ملف الصوت المخصص: {audio_file_to_use}")
                    elif self.audio_file and os.path.exists(self.audio_file):
                        audio_file_to_use = self.audio_file
                        print(f"استخدام ملف الصوت المحمل: {audio_file_to_use}")

                if audio_file_to_use:
                    print(f"المرحلة 3: دمج الصوت مع الفيديو (مستوى الصوت: {audio_volume*100}%)")
                    merged_segment = self.video_processor.merge_audio_with_video(
                        current_segment, audio_file_to_use, f"segment_{i+1}_step3_audio",
                        volume=audio_volume, audio_start_time=audio_start_time
                    )

                    if merged_segment:
                        current_segment = merged_segment
                        intermediate_files.append(merged_segment)

                        # إضافة المقطع النهائي مع الصوت إلى قائمة المقاطع النهائية مباشرة
                        # هذا يضمن أن المقاطع المنفصلة ستكون متاحة حتى إذا تم تطبيق تأثير التلاشي
                        if merged_segment not in final_segments:
                            final_segments.append(merged_segment)
                            print(f"تم إضافة المقطع النهائي المنفصل: {merged_segment}")

                # المرحلة 4: إضافة الكابشن
                if generate_captions:
                    print("المرحلة 4: إضافة كابشن للفيديو")

                    # استخدام النص المستخرج إذا كان متاحًا، وإلا استخدام النص المقدم
                    if self.recognized_text and self.params.get('use_recognized_text', False):
                        segment_caption = self.recognized_text
                    else:
                        # استخدام رقم المقطع في الكابشن
                        segment_caption = f"{caption_text} - {i+1}/{len(segments)}"

                    # الحصول على إعدادات الخط
                    font_style = self.params.get('font_style', 'complex')
                    font_scale = self.params.get('font_scale', 1.0)
                    font_thickness = self.params.get('font_thickness', 2)
                    text_color = self.params.get('text_color', (255, 255, 255))  # أبيض
                    bg_color = self.params.get('bg_color', (0, 0, 0))  # أسود
                    position = self.params.get('text_position', 'bottom')

                    captioned_segment = self.video_processor.add_caption_to_video(
                        current_segment, segment_caption, f"segment_{i+1}_step4_final",
                        font_style=font_style,
                        font_scale=font_scale,
                        font_thickness=font_thickness,
                        text_color=text_color,
                        bg_color=bg_color,
                        position=position
                    )

                    if captioned_segment:
                        current_segment = captioned_segment
                        intermediate_files.append(captioned_segment)

                # إضافة المقطع النهائي إلى القائمة (إذا لم يتم إضافته بالفعل)
                if current_segment not in final_segments:
                    final_segments.append(current_segment)
                    print(f"تم إضافة المقطع النهائي: {current_segment}")
                self.progress_updated.emit(30 + (i + 1) * 60 // len(segments))

            # المرحلة 5: تطبيق تأثير التلاشي بين المقاطع إذا كان مفعلاً
            crossfade_enabled = self.params.get('crossfade_enabled', False)
            if crossfade_enabled and len(final_segments) > 1:
                print("المرحلة 5: تطبيق تأثير التلاشي بين المقاطع...")
                crossfaded_segments = []

                # تطبيق تأثير التلاشي بين المقاطع المتتالية
                for i in range(len(final_segments) - 1):
                    print(f"تطبيق تأثير التلاشي بين المقطعين {i+1} و {i+2}")
                    crossfade_output = self.video_processor.apply_crossfade(
                        final_segments[i], final_segments[i+1],
                        f"segment_final_crossfade_{i+1}_to_{i+2}", fade_duration=15
                    )
                    if crossfade_output:
                        crossfaded_segments.append(crossfade_output)
                        intermediate_files.append(crossfade_output)

                if crossfaded_segments:
                    print(f"تم إنشاء {len(crossfaded_segments)} مقطع بتأثير التلاشي")

                    # إضافة المقاطع المدمجة إلى القائمة النهائية
                    for crossfade_segment in crossfaded_segments:
                        if crossfade_segment not in final_segments:
                            final_segments.append(crossfade_segment)

                    # تأكد من أن المقاطع الأصلية لا تزال في القائمة النهائية
                    for i, segment in enumerate(segments):
                        # استخدام مسار ديناميكي بدلاً من المسار الثابت
                        output_dir = os.path.dirname(segment)
                        segment_with_audio = os.path.join(output_dir, f"segment_{i+1}_step3_audio.mp4")
                        if os.path.exists(segment_with_audio) and segment_with_audio not in final_segments:
                            final_segments.append(segment_with_audio)
                            print(f"إضافة المقطع الأصلي إلى القائمة النهائية: {segment_with_audio}")

                    print(f"إجمالي المقاطع النهائية: {len(final_segments)} مقطع")

            # تخزين جميع الملفات المنتجة
            if save_intermediate:
                # حفظ جميع الملفات الوسيطة والنهائية
                self.generated_files = intermediate_files
                print(f"تم حفظ {len(intermediate_files)} ملف (بما في ذلك الملفات الوسيطة)")
            else:
                # حفظ الملفات النهائية فقط
                self.generated_files = final_segments

            # طباعة قائمة الملفات النهائية
            print("\nقائمة الملفات النهائية:")
            for i, file_path in enumerate(self.generated_files):
                print(f"{i+1}. {file_path}")

                # حذف الملفات الوسيطة إذا كان خيار حفظ الملفات الوسيطة غير مفعل
                try:
                    print("تنظيف الملفات الوسيطة...")
                    deleted_count = 0
                    failed_count = 0

                    # تحديد الملفات النهائية التي يجب الاحتفاظ بها
                    # الاحتفاظ بالمقاطع النهائية المنفصلة (segment_X_step3_audio.mp4)
                    # بالإضافة إلى مقاطع التلاشي (segment_final_crossfade_X_to_Y.mp4)
                    keep_files = set()

                    # إضافة جميع المقاطع النهائية إلى قائمة الملفات التي يجب الاحتفاظ بها
                    for file_path in final_segments:
                        keep_files.add(file_path)
                        print(f"الاحتفاظ بالملف النهائي: {file_path}")

                    # حذف الملفات الوسيطة
                    for file_path in intermediate_files:
                        # تحقق من أن الملف ليس من الملفات النهائية وأنه موجود
                        if file_path and os.path.exists(file_path) and file_path not in keep_files:
                            try:
                                print(f"حذف الملف الوسيط: {file_path}")
                                os.remove(file_path)
                                deleted_count += 1
                            except Exception as file_error:
                                print(f"فشل في حذف الملف: {file_path} - {str(file_error)}")
                                failed_count += 1

                    # تنظيف أي ملفات مؤقتة أخرى في مجلد المخرجات
                    output_dir = os.path.dirname(final_segments[0]) if final_segments else self.output_dir
                    for filename in os.listdir(output_dir):
                        if filename.startswith("temp_") or filename.endswith(".tmp"):
                            try:
                                temp_file = os.path.join(output_dir, filename)
                                if os.path.exists(temp_file) and temp_file not in keep_files:
                                    print(f"حذف ملف مؤقت: {temp_file}")
                                    os.remove(temp_file)
                                    deleted_count += 1
                            except Exception as temp_error:
                                print(f"فشل في حذف الملف المؤقت: {filename} - {str(temp_error)}")
                                failed_count += 1

                    print(f"تم حذف {deleted_count} ملف وسيط بنجاح")
                    if failed_count > 0:
                        print(f"فشل في حذف {failed_count} ملف")
                    print(f"تم الاحتفاظ بـ {len(final_segments)} ملف نهائي فقط")
                except Exception as e:
                    print(f"خطأ في تنظيف الملفات الوسيطة: {str(e)}")

            self.progress_updated.emit(100)
            self.processing_completed.emit(
                True,
                f"تم إنشاء {len(final_segments)} مقاطع بنجاح",
                self.generated_files
            )

        except Exception as e:
            import traceback
            print(f"Error generating videos: {str(e)}")
            print(traceback.format_exc())
            self.processing_completed.emit(False, str(e), [])


class MainWindow(QMainWindow):
    """Main window for the MekaVido application."""

    def __init__(self):
        super().__init__()

        self.setWindowTitle("MekaVido - مقاطع فيديو قصيرة")
        self.setMinimumSize(1000, 800)  # زيادة الحجم الأدنى للنافذة
        # إزالة تثبيت حجم النافذة للسماح بتكبير النافذة
        # تفعيل أزرار التحكم في النافذة (تصغير، تكبير، إغلاق)
        self.setWindowFlags(
            Qt.Window |                  # نافذة عادية
            Qt.WindowCloseButtonHint |   # زر الإغلاق
            Qt.WindowMinimizeButtonHint | # زر التصغير
            Qt.WindowMaximizeButtonHint   # زر التكبير
        )
        # السماح بتغيير حجم النافذة
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # إضافة خلفية متدرجة للتطبيق
        self.setAutoFillBackground(True)
        palette = self.palette()
        gradient = "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f5f7fa, stop:1 #e4e8f0)"
        palette.setColor(self.backgroundRole(), QColor(gradient))
        self.setPalette(palette)

        # Set application style
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: #333;
                min-width: 120px;
                max-width: 120px;
            }
            QPushButton {
                background-color: #4a86e8;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-height: 30px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
            QPushButton:pressed {
                background-color: #2a66c8;
            }
            QLineEdit, QSpinBox, QComboBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 6px;
                background-color: white;
                min-height: 30px;
                min-width: 200px;
            }
            QLineEdit:focus, QSpinBox:focus, QComboBox:focus {
                border: 1px solid #4a86e8;
            }
            QLineEdit:hover, QSpinBox:hover, QComboBox:hover {
                border: 1px solid #bbb;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #ccc;
                border-radius: 8px;
                margin-top: 20px;
                background-color: #ffffff;
                padding: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                color: #4a86e8;
                font-size: 14px;
            }
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 4px;
                text-align: center;
                height: 25px;
                background-color: white;
                min-height: 25px;
            }
            QProgressBar::chunk {
                background-color: #4a86e8;
                width: 10px;
                margin: 0.5px;
            }
            QListWidget {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
                min-height: 150px;
            }
            QCheckBox {
                spacing: 5px;
                min-height: 25px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 1px solid #ccc;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #4a86e8;
                background-color: #4a86e8;
                border-radius: 3px;
            }
            QHBoxLayout {
                margin: 5px;
                spacing: 10px;
            }
            QVBoxLayout {
                margin: 5px;
                spacing: 10px;
            }
        """)

        # Initialize processing thread
        self.processing_thread = VideoProcessingThread()
        self.processing_thread.progress_updated.connect(self.update_progress)
        self.processing_thread.processing_completed.connect(self.processing_finished)

        # Initialize output directory
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "output")
        os.makedirs(self.output_dir, exist_ok=True)

        # Store file paths
        self.video_file = None
        self.audio_file = None
        self.custom_audio_file = None  # ملف الصوت المخصص الذي يختاره المستخدم
        self.audio_volume = 1.0  # مستوى الصوت (1.0 = 100%)
        self.generated_files = []

        # إعدادات الكابشن الافتراضية (تم تعطيل الكابشن)
        self.text_color = (255, 255, 255)  # أبيض
        self.bg_color = (0, 0, 0)  # أسود

        # تنظيف الملفات المؤقتة عند بدء التطبيق
        self.clean_temp_files()

        # Set up the UI
        self.setup_ui()

        # التحقق من حالة الفيديو عند بدء التطبيق
        self.check_video_status()

    def setup_ui(self):
        """Set up the user interface."""
        # Main widget and layout with scroll area
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Create a scroll area for the content with improved styling
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)  # Remove frame border

        # تحسين مظهر شريط التمرير
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                margin: 0px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                min-height: 30px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """)

        # Create a widget for the scroll area content
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(15)
        scroll_layout.setContentsMargins(5, 5, 5, 5)

        # Set the content widget to the scroll area
        scroll_area.setWidget(scroll_content)

        # Add the scroll area to the main layout
        main_layout.addWidget(scroll_area)

        # Set the main widget as the central widget
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

        # We'll use scroll_layout instead of main_layout for adding our widgets
        self.scroll_layout = scroll_layout

        # Input section with fixed layout
        input_group = QGroupBox("إدخال الفيديو")
        input_layout = QVBoxLayout()
        input_layout.setSpacing(15)  # زيادة المسافة بين العناصر

        # Video URL input - تحسين التخطيط
        video_layout = QHBoxLayout()
        video_layout.setContentsMargins(5, 5, 5, 5)
        video_label = QLabel("رابط الفيديو الأساسي:")
        video_label.setFixedWidth(150)  # تثبيت عرض التسمية

        self.video_url_input = QLineEdit()
        self.video_url_input.setPlaceholderText("أدخل رابط الفيديو من يوتيوب")
        self.video_url_input.setMinimumWidth(400)  # تحديد الحد الأدنى للعرض

        video_layout.addWidget(video_label)
        video_layout.addWidget(self.video_url_input)
        video_layout.addStretch(1)  # إضافة مساحة مرنة
        input_layout.addLayout(video_layout)

        # Audio URL input - تحسين التخطيط
        audio_layout = QHBoxLayout()
        audio_layout.setContentsMargins(5, 5, 5, 5)
        audio_label = QLabel("رابط الصوت:")
        audio_label.setFixedWidth(150)  # تثبيت عرض التسمية

        self.audio_url_input = QLineEdit()
        self.audio_url_input.setPlaceholderText("أدخل رابط الفيديو الصوتي من يوتيوب")
        self.audio_url_input.setMinimumWidth(400)  # تحديد الحد الأدنى للعرض

        # Audio download type
        self.audio_download_type = QComboBox()
        self.audio_download_type.addItem("تحميل الصوت فقط", "audio_only")
        self.audio_download_type.addItem("تحميل الفيديو واستخراج الصوت", "extract_from_video")
        self.audio_download_type.setFixedWidth(200)  # تثبيت عرض القائمة المنسدلة

        audio_layout.addWidget(audio_label)
        audio_layout.addWidget(self.audio_url_input)
        audio_layout.addWidget(self.audio_download_type)
        input_layout.addLayout(audio_layout)

        # إضافة خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        input_layout.addWidget(separator)

        # Local file selection - تحسين التخطيط
        local_file_layout = QHBoxLayout()
        local_file_layout.setContentsMargins(5, 5, 5, 5)
        local_file_label = QLabel("استخدام ملف محلي:")
        local_file_label.setFixedWidth(150)  # تثبيت عرض التسمية

        self.use_local_file_checkbox = QCheckBox("استخدام ملف موجود على الجهاز")
        self.use_local_file_checkbox.setFixedWidth(200)  # تثبيت عرض مربع الاختيار

        self.select_local_file_button = QPushButton("اختيار ملف")
        self.select_local_file_button.clicked.connect(self.select_local_file)
        self.select_local_file_button.setFixedWidth(120)  # تثبيت عرض الزر
        self.select_local_file_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e68a00;
            }
            QPushButton:pressed {
                background-color: #cc7a00;
            }
        """)

        self.local_file_path = None

        local_file_layout.addWidget(local_file_label)
        local_file_layout.addWidget(self.use_local_file_checkbox)
        local_file_layout.addWidget(self.select_local_file_button)
        local_file_layout.addStretch(1)  # إضافة مساحة مرنة
        input_layout.addLayout(local_file_layout)

        # Add file path display - تحسين التخطيط
        file_path_layout = QHBoxLayout()
        file_path_layout.setContentsMargins(5, 5, 5, 5)
        file_path_label = QLabel("مسار الملف المحلي:")
        file_path_label.setFixedWidth(150)  # تثبيت عرض التسمية

        self.local_file_display = QLineEdit()
        self.local_file_display.setPlaceholderText("لم يتم اختيار ملف بعد")
        self.local_file_display.setReadOnly(True)
        self.local_file_display.setMinimumWidth(600)  # تحديد الحد الأدنى للعرض

        file_path_layout.addWidget(file_path_label)
        file_path_layout.addWidget(self.local_file_display)
        input_layout.addLayout(file_path_layout)

        # Download buttons with improved styling and layout
        download_buttons_layout = QHBoxLayout()
        download_buttons_layout.setContentsMargins(5, 10, 5, 5)
        download_buttons_layout.setSpacing(20)  # زيادة المسافة بين الأزرار

        # Download button
        self.download_button = QPushButton("تحميل الفيديوهات")
        self.download_button.clicked.connect(self.download_videos)
        self.download_button.setFixedSize(180, 40)  # تثبيت حجم الزر

        # Reload button - زر إعادة تحميل الفيديو
        self.reload_button = QPushButton("إعادة تحميل الفيديو")
        self.reload_button.clicked.connect(self.reload_video)
        self.reload_button.setFixedSize(180, 40)  # تثبيت حجم الزر
        self.download_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
            QPushButton:pressed {
                background-color: #0a69b7;
            }
        """)



        # إضافة مساحات مرنة قبل وبعد الأزرار لتوسيطها
        download_buttons_layout.addStretch(1)
        download_buttons_layout.addWidget(self.download_button)
        download_buttons_layout.addWidget(self.reload_button)
        download_buttons_layout.addStretch(1)

        # تعيين نمط زر إعادة التحميل
        self.reload_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #EF6C00;
            }
            QPushButton:disabled {
                background-color: #FFE0B2;
                color: #BDBDBD;
            }
        """)

        input_layout.addLayout(download_buttons_layout)

        input_group.setLayout(input_layout)
        self.scroll_layout.addWidget(input_group)

        # Settings section - تحسين التخطيط
        settings_group = QGroupBox("إعدادات المقاطع")
        settings_layout = QVBoxLayout()
        settings_layout.setSpacing(15)  # زيادة المسافة بين العناصر

        # إنشاء تخطيط بعمودين لإعدادات المقاطع
        settings_grid_layout = QGridLayout()
        settings_grid_layout.setHorizontalSpacing(20)
        settings_grid_layout.setVerticalSpacing(10)

        # العمود الأول - إعدادات أساسية

        # Duration setting
        duration_label = QLabel("مدة كل مقطع (بالثواني):")
        duration_label.setFixedWidth(180)
        self.duration_input = QSpinBox()
        self.duration_input.setMinimum(5)
        self.duration_input.setMaximum(300)  # زيادة الحد الأقصى إلى 5 دقائق (300 ثانية)
        self.duration_input.setValue(15)
        self.duration_input.setFixedWidth(100)
        settings_grid_layout.addWidget(duration_label, 0, 0)
        settings_grid_layout.addWidget(self.duration_input, 0, 1)

        # Count setting
        count_label = QLabel("عدد المقاطع المطلوبة:")
        count_label.setFixedWidth(180)
        self.count_input = QSpinBox()
        self.count_input.setMinimum(1)
        self.count_input.setMaximum(20)
        self.count_input.setValue(5)
        self.count_input.setFixedWidth(100)
        settings_grid_layout.addWidget(count_label, 1, 0)
        settings_grid_layout.addWidget(self.count_input, 1, 1)

        # Start minute setting
        start_minute_label = QLabel("البدء من الدقيقة:")
        start_minute_label.setFixedWidth(180)
        self.start_minute_input = QSpinBox()
        self.start_minute_input.setMinimum(0)
        self.start_minute_input.setMaximum(180)  # 3 hours max
        self.start_minute_input.setValue(0)
        self.start_minute_input.setFixedWidth(100)
        settings_grid_layout.addWidget(start_minute_label, 2, 0)
        settings_grid_layout.addWidget(self.start_minute_input, 2, 1)

        # Interval between segments
        interval_label = QLabel("الفاصل بين المقاطع (بالثواني):")
        interval_label.setFixedWidth(180)
        self.interval_input = QSpinBox()
        self.interval_input.setMinimum(0)
        self.interval_input.setMaximum(300)  # 5 minutes max
        self.interval_input.setValue(60)  # Default 1 minute
        self.interval_input.setFixedWidth(100)
        settings_grid_layout.addWidget(interval_label, 3, 0)
        settings_grid_layout.addWidget(self.interval_input, 3, 1)

        # العمود الثاني - إعدادات إضافية

        # Video quality settings
        quality_label = QLabel("جودة الفيديو:")
        quality_label.setFixedWidth(180)
        self.quality_combo = QComboBox()
        self.quality_combo.addItem("عالية (1080p)", "high")
        self.quality_combo.addItem("متوسطة (720p)", "medium")
        self.quality_combo.addItem("منخفضة (480p)", "low")
        self.quality_combo.setFixedWidth(150)
        settings_grid_layout.addWidget(quality_label, 0, 2)
        settings_grid_layout.addWidget(self.quality_combo, 0, 3)

        # Effects settings
        effects_label = QLabel("المؤثرات:")
        effects_label.setFixedWidth(180)
        self.effects_combo = QComboBox()
        self.effects_combo.addItem("بدون مؤثرات")
        self.effects_combo.addItem("أبيض وأسود")
        self.effects_combo.addItem("زيادة السطوع")
        self.effects_combo.addItem("زيادة التباين")
        self.effects_combo.addItem("عكس الفيديو")
        self.effects_combo.addItem("تأثير سيبيا")
        self.effects_combo.addItem("تأثير الحافة")
        self.effects_combo.addItem("تأثير الضباب")
        self.effects_combo.addItem("تأثير الحركة البطيئة")
        self.effects_combo.addItem("تأثير الحركة السريعة")
        self.effects_combo.addItem("تأثير التموج")
        self.effects_combo.addItem("تأثير الدوران")
        self.effects_combo.addItem("تأثير التلاشي")
        self.effects_combo.setFixedWidth(150)
        settings_grid_layout.addWidget(effects_label, 1, 2)
        settings_grid_layout.addWidget(self.effects_combo, 1, 3)

        # Portrait mode setting
        portrait_label = QLabel("تنسيق الفيديو:")
        portrait_label.setFixedWidth(180)
        self.portrait_checkbox = QCheckBox("تنسيق عمودي 9:16 (ريلز/شورتس)")
        self.portrait_checkbox.setChecked(True)
        settings_grid_layout.addWidget(portrait_label, 2, 2)
        settings_grid_layout.addWidget(self.portrait_checkbox, 2, 3)

        # Sequential mode checkbox
        sequential_label = QLabel("طريقة التقطيع:")
        sequential_label.setFixedWidth(180)
        self.sequential_checkbox = QCheckBox("مقاطع متتالية (لتغطية الصوت بالكامل)")
        self.sequential_checkbox.setChecked(False)
        settings_grid_layout.addWidget(sequential_label, 3, 2)
        settings_grid_layout.addWidget(self.sequential_checkbox, 3, 3)

        # Cross fade checkbox
        crossfade_label = QLabel("تأثير الانتقال:")
        crossfade_label.setFixedWidth(180)
        self.crossfade_checkbox = QCheckBox("تأثير التلاشي بين المقاطع (Cross Fade)")
        self.crossfade_checkbox.setChecked(False)
        settings_grid_layout.addWidget(crossfade_label, 4, 2)
        settings_grid_layout.addWidget(self.crossfade_checkbox, 4, 3)

        # Save intermediate files checkbox
        intermediate_label = QLabel("حفظ الملفات الوسيطة:")
        intermediate_label.setFixedWidth(180)
        self.intermediate_checkbox = QCheckBox("حفظ جميع الملفات الوسيطة")
        self.intermediate_checkbox.setChecked(False)
        settings_grid_layout.addWidget(intermediate_label, 5, 2)
        settings_grid_layout.addWidget(self.intermediate_checkbox, 5, 3)

        # إضافة التخطيط الشبكي إلى تخطيط الإعدادات
        settings_layout.addLayout(settings_grid_layout)

        # إضافة خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        settings_layout.addWidget(separator)

        # Audio settings - إعدادات الصوت
        audio_group = QGroupBox("إعدادات الصوت")
        audio_group.setStyleSheet("""
            QGroupBox {
                background-color: #fff0f5;
                border: 1px solid #ffb6c1;
                border-radius: 6px;
                margin-top: 20px;
            }
            QGroupBox::title {
                color: #d81b60;
            }
        """)
        audio_layout = QVBoxLayout()
        audio_layout.setSpacing(10)

        # Audio enable checkbox
        audio_enable_layout = QHBoxLayout()
        audio_label = QLabel("الصوت:")
        audio_label.setFixedWidth(150)
        self.audio_checkbox = QCheckBox("إضافة صوت للفيديو")
        self.audio_checkbox.setChecked(True)
        audio_enable_layout.addWidget(audio_label)
        audio_enable_layout.addWidget(self.audio_checkbox)
        audio_enable_layout.addStretch(1)
        audio_layout.addLayout(audio_enable_layout)

        # Audio file selection
        audio_file_layout = QHBoxLayout()
        audio_file_label = QLabel("ملف الصوت:")
        audio_file_label.setFixedWidth(150)
        self.audio_file_path = QLineEdit()
        self.audio_file_path.setPlaceholderText("اختر ملف صوت محلي")
        self.audio_file_path.setReadOnly(True)
        self.audio_file_path.setMinimumWidth(400)

        self.select_audio_button = QPushButton("اختيار ملف")
        self.select_audio_button.clicked.connect(self.select_audio_file)
        self.select_audio_button.setFixedWidth(100)
        self.select_audio_button.setStyleSheet("""
            QPushButton {
                background-color: #d81b60;
                color: white;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c2185b;
            }
            QPushButton:pressed {
                background-color: #ad1457;
            }
        """)

        audio_file_layout.addWidget(audio_file_label)
        audio_file_layout.addWidget(self.audio_file_path)
        audio_file_layout.addWidget(self.select_audio_button)
        audio_layout.addLayout(audio_file_layout)

        # Audio start time setting
        audio_start_layout = QHBoxLayout()
        audio_start_label = QLabel("بدء الصوت من الدقيقة:")
        audio_start_label.setFixedWidth(150)

        self.audio_start_minute_input = QSpinBox()
        self.audio_start_minute_input.setMinimum(0)
        self.audio_start_minute_input.setMaximum(180)  # 3 hours max
        self.audio_start_minute_input.setValue(0)
        self.audio_start_minute_input.setFixedWidth(100)

        # إضافة خيار لاستخدام نفس نقطة بداية الفيديو
        self.use_video_start_checkbox = QCheckBox("استخدام نفس نقطة بداية الفيديو")
        self.use_video_start_checkbox.setChecked(True)
        self.use_video_start_checkbox.stateChanged.connect(self.toggle_audio_start_time)

        audio_start_layout.addWidget(audio_start_label)
        audio_start_layout.addWidget(self.audio_start_minute_input)
        audio_start_layout.addWidget(self.use_video_start_checkbox)
        audio_layout.addLayout(audio_start_layout)

        # تعطيل حقل بداية الصوت بشكل افتراضي
        self.audio_start_minute_input.setEnabled(False)

        # Audio volume slider
        audio_volume_layout = QHBoxLayout()
        audio_volume_label = QLabel("مستوى الصوت:")
        audio_volume_label.setFixedWidth(150)

        self.audio_volume_slider = QSlider(Qt.Horizontal)
        self.audio_volume_slider.setRange(0, 200)  # 0% إلى 200%
        self.audio_volume_slider.setValue(100)  # القيمة الافتراضية 100%
        self.audio_volume_slider.setTickPosition(QSlider.TicksBelow)
        self.audio_volume_slider.setTickInterval(25)
        self.audio_volume_slider.setMinimumWidth(300)
        self.audio_volume_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #bbb;
                height: 8px;
                background: #f0f0f0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #d81b60;
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -6px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: #c2185b;
            }
            QSlider::add-page:horizontal {
                background: #f0f0f0;
                border-radius: 4px;
            }
            QSlider::sub-page:horizontal {
                background: #ffcdd2;
                border-radius: 4px;
            }
        """)

        self.audio_volume_label = QLabel("100%")
        self.audio_volume_label.setFixedWidth(50)
        self.audio_volume_label.setAlignment(Qt.AlignCenter)
        self.audio_volume_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #d81b60;
                background-color: #f0f0f0;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 2px;
            }
        """)

        self.audio_volume_slider.valueChanged.connect(self.update_audio_volume)

        audio_volume_layout.addWidget(audio_volume_label)
        audio_volume_layout.addWidget(self.audio_volume_slider)
        audio_volume_layout.addWidget(self.audio_volume_label)
        audio_layout.addLayout(audio_volume_layout)

        # إضافة قسم حساب عدد المقاطع بناءً على الصوت
        audio_segments_group = QGroupBox("حساب عدد المقاطع بناءً على الصوت")
        audio_segments_group.setStyleSheet("""
            QGroupBox {
                background-color: #E3F2FD;
                border: 1px solid #90CAF9;
                border-radius: 8px;
                margin-top: 15px;
                padding: 10px;
            }
            QGroupBox::title {
                color: #1565C0;
                font-weight: bold;
                font-size: 14px;
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 10px;
                background-color: #E3F2FD;
            }
        """)
        audio_segments_layout = QVBoxLayout()

        # إضافة شرح للميزة
        audio_segments_info = QLabel(
            "هذه الميزة تساعدك على حساب عدد المقاطع الممكنة بناءً على مدة الصوت المتوفر، "
            "مع مراعاة مدة كل مقطع والفاصل الزمني بين المقاطع."
        )
        audio_segments_info.setWordWrap(True)
        audio_segments_info.setStyleSheet("""
            background-color: #BBDEFB;
            color: #0D47A1;
            padding: 10px;
            border-radius: 6px;
            font-size: 13px;
            border: 1px solid #64B5F6;
        """)
        audio_segments_layout.addWidget(audio_segments_info)

        # إضافة زر لحساب عدد المقاطع
        calculate_segments_layout = QHBoxLayout()
        self.calculate_segments_button = QPushButton("حساب عدد المقاطع الممكنة")
        self.calculate_segments_button.clicked.connect(self.calculate_possible_segments)
        self.calculate_segments_button.setFixedSize(200, 40)  # تثبيت حجم الزر
        self.calculate_segments_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 16px;
                border-radius: 8px;
                border: 1px solid #1976D2;
            }
            QPushButton:hover {
                background-color: #1E88E5;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)

        # إضافة حقل لعرض النتيجة
        segments_result_layout = QVBoxLayout()

        self.segments_result_label = QLabel("عدد المقاطع الممكنة: -")
        self.segments_result_label.setStyleSheet("""
            font-weight: bold;
            color: #1976D2;
            font-size: 16px;
            background-color: #E3F2FD;
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #BBDEFB;
        """)

        # إضافة شريط تقدم لعرض عدد المقاطع بشكل مرئي
        self.segments_progress_bar = QProgressBar()
        self.segments_progress_bar.setRange(0, 100)
        self.segments_progress_bar.setValue(0)
        self.segments_progress_bar.setTextVisible(True)
        self.segments_progress_bar.setFormat("%v من %m مقطع")
        self.segments_progress_bar.setFixedHeight(20)
        self.segments_progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #BBDEFB;
                border-radius: 5px;
                text-align: center;
                background-color: #E3F2FD;
                color: #1976D2;
                font-weight: bold;
                font-size: 12px;
                padding: 1px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                                stop:0 #2196F3, stop:0.5 #64B5F6, stop:1 #1976D2);
                border-radius: 4px;
                margin: 0.5px;
            }
        """)

        segments_result_layout.addWidget(self.segments_result_label)
        segments_result_layout.addWidget(self.segments_progress_bar)

        # إضافة زر الحساب إلى التخطيط الأفقي
        calculate_segments_layout.addWidget(self.calculate_segments_button)
        calculate_segments_layout.addLayout(segments_result_layout)
        calculate_segments_layout.addStretch(1)

        audio_segments_layout.addLayout(calculate_segments_layout)
        audio_segments_group.setLayout(audio_segments_layout)
        audio_layout.addWidget(audio_segments_group)

        # تعيين تخطيط الصوت للمجموعة
        audio_group.setLayout(audio_layout)

        # إضافة مجموعة الصوت إلى تخطيط الإعدادات
        settings_layout.addWidget(audio_group)



        settings_group.setLayout(settings_layout)
        self.scroll_layout.addWidget(settings_group)

        # Generate button with improved styling
        generate_button_layout = QHBoxLayout()
        generate_button_layout.setContentsMargins(0, 20, 0, 20)  # زيادة المسافة قبل وبعد الزر

        self.generate_button = QPushButton("توليد المقاطع")
        self.generate_button.clicked.connect(self.generate_videos)
        self.generate_button.setFixedSize(250, 50)  # تثبيت حجم الزر
        self.generate_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 10px;
                border: 2px solid #3d8b40;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #a5d6a7;
                color: #e8f5e9;
                border: 2px solid #a5d6a7;
            }
        """)

        # إضافة أيقونة للزر (يمكن استبدالها بأيقونة حقيقية لاحقًا)
        self.generate_button.setIcon(QIcon.fromTheme("media-playback-start"))
        self.generate_button.setIconSize(QSize(24, 24))

        # إضافة أيقونة للزر (يمكن استبدالها بأيقونة حقيقية لاحقًا)
        generate_button_layout.addStretch()
        generate_button_layout.addWidget(self.generate_button)
        generate_button_layout.addStretch()
        self.scroll_layout.addLayout(generate_button_layout)

        # Progress bar with improved styling
        progress_group = QGroupBox("تقدم العملية")
        progress_group.setStyleSheet("""
            QGroupBox {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 6px;
                margin-top: 10px;
            }
            QGroupBox::title {
                color: #555;
            }
        """)

        progress_layout = QVBoxLayout()
        progress_layout.setContentsMargins(20, 20, 20, 20)  # زيادة الهوامش

        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p% مكتمل")
        self.progress_bar.setFixedHeight(30)  # تثبيت ارتفاع شريط التقدم
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 10px;
                text-align: center;
                background-color: #f0f0f0;
                color: #333;
                font-weight: bold;
                font-size: 14px;
                padding: 2px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                                stop:0 #4CAF50, stop:0.5 #8BC34A, stop:1 #2196F3);
                border-radius: 8px;
                margin: 1px;
            }
        """)

        # إضافة نص وصفي لحالة التقدم
        self.progress_status = QLabel("جاهز للبدء...")
        self.progress_status.setAlignment(Qt.AlignCenter)
        self.progress_status.setStyleSheet("""
            font-size: 13px;
            color: #555;
            margin-top: 5px;
        """)

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.progress_status)

        progress_group.setLayout(progress_layout)
        self.scroll_layout.addWidget(progress_group)

        # Output section with improved styling
        output_group = QGroupBox("المخرجات")
        output_group.setStyleSheet("""
            QGroupBox {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 6px;
                margin-top: 10px;
            }
            QGroupBox::title {
                color: #555;
                font-weight: bold;
            }
        """)

        output_layout = QVBoxLayout()
        output_layout.setContentsMargins(15, 15, 15, 15)  # زيادة الهوامش

        # Output list with improved styling
        output_list_label = QLabel("الملفات المنتجة:")
        output_list_label.setAlignment(Qt.AlignCenter)
        output_list_label.setStyleSheet("font-weight: bold; color: #555;")

        self.output_list = QListWidget()
        self.output_list.setMinimumHeight(150)  # تحديد الحد الأدنى للارتفاع
        self.output_list.setAlternatingRowColors(True)  # تلوين الصفوف بالتناوب
        self.output_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 8px;
                padding: 8px;
                font-size: 13px;
            }
            QListWidget::item {
                padding: 8px;
                margin: 2px 0;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:hover {
                background-color: #f5f5f5;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
                color: #0d47a1;
                border-left: 3px solid #2196F3;
            }
            QListWidget::item:alternate {
                background-color: #f9f9f9;
            }
        """)

        output_layout.addWidget(output_list_label)
        output_layout.addWidget(self.output_list)

        # Open output folder button with improved styling
        open_folder_layout = QHBoxLayout()
        open_folder_layout.setContentsMargins(0, 10, 0, 0)  # زيادة المسافة فوق الزر

        self.open_folder_button = QPushButton("فتح مجلد المخرجات")
        self.open_folder_button.clicked.connect(self.open_output_folder)
        self.open_folder_button.setFixedSize(180, 40)  # تثبيت حجم الزر
        self.open_folder_button.setStyleSheet("""
            QPushButton {
                background-color: #607D8B;
                color: white;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 16px;
                border-radius: 8px;
                border: 1px solid #455A64;
            }
            QPushButton:hover {
                background-color: #546E7A;
            }
            QPushButton:pressed {
                background-color: #455A64;
            }
        """)

        # إضافة أيقونة للزر
        self.open_folder_button.setIcon(QIcon.fromTheme("folder-open"))
        self.open_folder_button.setIconSize(QSize(20, 20))

        open_folder_layout.addStretch()
        open_folder_layout.addWidget(self.open_folder_button)
        open_folder_layout.addStretch()
        output_layout.addLayout(open_folder_layout)

        output_group.setLayout(output_layout)
        self.scroll_layout.addWidget(output_group)

        # Set layout direction for Arabic
        main_widget.setLayoutDirection(Qt.RightToLeft)

    def download_videos(self):
        """Download videos from the provided URLs."""
        # Check if using local file
        if self.use_local_file_checkbox.isChecked() and self.local_file_path:
            # If local file is selected, we don't need to download
            QMessageBox.information(self, "استخدام ملف محلي", "سيتم استخدام الملف المحلي المحدد")

            # Enable generate button if video file is selected
            if self.video_file:
                self.generate_button.setEnabled(True)

            return

        video_url = self.video_url_input.text().strip()
        audio_url = self.audio_url_input.text().strip()

        if not video_url and not audio_url:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال رابط الفيديو الأساسي أو رابط الصوت")
            return

        # Get selected quality
        quality = self.quality_combo.currentData()
        print(f"Selected quality: {quality}")

        # Get audio download type
        audio_download_type = self.audio_download_type.currentData()
        print(f"Audio download type: {audio_download_type}")

        # Clear previous files
        self.video_file = None
        self.audio_file = None
        self.generated_files = []

        # Clear output list
        self.output_list.clear()

        # Reset progress bar
        self.progress_bar.setValue(0)

        # Disable buttons during processing
        self.download_button.setEnabled(False)
        self.generate_button.setEnabled(False)

        # Set up and start the processing thread
        self.processing_thread.setup_task('download', {
            'video_url': video_url,
            'audio_url': audio_url,
            'quality': quality,
            'audio_download_type': audio_download_type
        })
        self.processing_thread.start()

    def generate_videos(self):
        """Generate video segments with the specified settings."""
        # Check if videos are downloaded or local file is selected
        if not self.video_file:
            error_msg = QMessageBox()
            error_msg.setIcon(QMessageBox.Critical)
            error_msg.setWindowTitle("خطأ في تحميل الفيديو الأساسي")
            error_msg.setText("لم يتم العثور على فيديو أساسي للمعالجة.")
            error_msg.setInformativeText("يرجى تحميل فيديو أولاً باستخدام أحد الخيارات التالية:\n"
                                        "1. إدخال رابط فيديو يوتيوب والنقر على زر 'تحميل الفيديوهات'\n"
                                        "2. تفعيل خيار 'استخدام ملف محلي' واختيار ملف فيديو من جهازك")
            error_msg.setStandardButtons(QMessageBox.Ok)
            error_msg.exec_()

            # تلوين حقل إدخال رابط الفيديو باللون الأحمر للفت انتباه المستخدم
            self.video_url_input.setStyleSheet("""
                QLineEdit {
                    background-color: #ffebee;
                    border: 1px solid #f44336;
                    padding: 8px;
                    border-radius: 6px;
                }
            """)

            # تلوين مربع تحديد استخدام ملف محلي باللون الأحمر
            self.use_local_file_checkbox.setStyleSheet("""
                QCheckBox {
                    color: #f44336;
                    font-weight: bold;
                }
            """)

            return

        # Get basic settings
        duration = self.duration_input.value()
        count = self.count_input.value()
        effect = self.effects_combo.currentText()
        generate_captions = False  # تعطيل الكابشن
        portrait_mode = self.portrait_checkbox.isChecked()
        caption_text = ""  # نص كابشن فارغ

        # تفعيل وضع التحويل المباشر إلى عمودي
        direct_portrait = True  # تفعيل بشكل افتراضي لتسريع العملية

        # Get audio settings
        use_audio = self.audio_checkbox.isChecked()
        audio_file_to_use = None

        # تحديد ملف الصوت الذي سيتم استخدامه
        if use_audio:
            if self.custom_audio_file and os.path.exists(self.custom_audio_file):
                audio_file_to_use = self.custom_audio_file
                print(f"استخدام ملف الصوت المخصص: {audio_file_to_use}")
            elif self.audio_file and os.path.exists(self.audio_file):
                audio_file_to_use = self.audio_file
                print(f"استخدام ملف الصوت المحمل: {audio_file_to_use}")
            else:
                print("لا يوجد ملف صوت متاح")

        # الحصول على مستوى الصوت
        audio_volume = self.audio_volume

        # Get advanced cutting settings
        start_minute = self.start_minute_input.value()
        interval_between_segments = self.interval_input.value()
        sequential_mode = self.sequential_checkbox.isChecked()
        save_intermediate = self.intermediate_checkbox.isChecked()
        crossfade_enabled = self.crossfade_checkbox.isChecked()

        # Get audio start time
        use_video_start = self.use_video_start_checkbox.isChecked()
        if use_video_start:
            audio_start_minute = start_minute
        else:
            audio_start_minute = self.audio_start_minute_input.value()

        # Caption settings (تم تعطيل الكابشن)
        use_recognized_text = False
        font_style = "complex"
        font_scale = 1.0
        font_thickness = 2
        text_position = "bottom"

        # Disable buttons during processing
        self.download_button.setEnabled(False)
        self.generate_button.setEnabled(False)

        # Update progress bar
        self.progress_bar.setValue(0)

        # طباعة معلومات تشخيصية عن ملف الفيديو
        print(f"معلومات ملف الفيديو قبل بدء المعالجة:")
        print(f"مسار الملف: {self.video_file}")
        print(f"هل الملف موجود: {os.path.exists(self.video_file) if self.video_file else False}")
        if self.video_file and os.path.exists(self.video_file):
            print(f"حجم الملف: {os.path.getsize(self.video_file) / (1024*1024):.2f} ميجابايت")
            print(f"امتداد الملف: {os.path.splitext(self.video_file)[1]}")

        # Set up and start the processing thread
        self.processing_thread.setup_task('generate', {
            # Basic settings
            'duration': duration,
            'count': count,
            'effect': effect,
            'generate_captions': generate_captions,
            'portrait_mode': portrait_mode,
            'caption_text': caption_text,

            # Advanced cutting settings
            'start_minute': start_minute,
            'interval_between_segments': interval_between_segments,
            'sequential_mode': sequential_mode,
            'save_intermediate': save_intermediate,

            # Caption settings
            'use_recognized_text': use_recognized_text,
            'font_style': font_style,
            'font_scale': font_scale,
            'font_thickness': font_thickness,
            'text_color': self.text_color,
            'bg_color': self.bg_color,
            'text_position': text_position,

            # Audio settings
            'use_audio': use_audio,
            'audio_file': audio_file_to_use,
            'audio_volume': audio_volume,
            'audio_start_minute': audio_start_minute,

            # تفعيل وضع التحويل المباشر إلى عمودي
            'direct_portrait': direct_portrait,

            # تفعيل تأثير التلاشي بين المقاطع
            'crossfade_enabled': crossfade_enabled,

            # تمرير ملف الفيديو مباشرة
            'video_file': self.video_file
        })
        self.processing_thread.start()

    def update_progress(self, value):
        """Update the progress bar and status text."""
        self.progress_bar.setValue(value)

        # تحديث النص الوصفي حسب قيمة التقدم
        if value == 0:
            self.progress_status.setText("جاري التحضير...")
        elif value < 20:
            self.progress_status.setText("جاري بدء العملية...")
        elif value < 50:
            self.progress_status.setText("جاري معالجة الفيديو...")
        elif value < 80:
            self.progress_status.setText("جاري تطبيق التأثيرات...")
        elif value < 100:
            self.progress_status.setText("جاري إنهاء المعالجة...")
        else:
            self.progress_status.setText("اكتملت العملية بنجاح!")

    def processing_finished(self, success, message, files=None):
        """Handle processing completion."""
        # Re-enable buttons
        self.download_button.setEnabled(True)
        self.generate_button.setEnabled(True)

        if success:
            QMessageBox.information(self, "نجاح", message)

            # Update output list with actual files
            if files:
                self.output_list.clear()
                for file_path in files:
                    if file_path and os.path.exists(file_path):
                        file_name = os.path.basename(file_path)
                        self.output_list.addItem(file_name)

                # Store the downloaded files for later use
                if self.processing_thread.task == 'download':
                    self.video_file = self.processing_thread.video_file
                    self.audio_file = self.processing_thread.audio_file

                    # Enable generate button if video was downloaded
                    if self.video_file:
                        self.generate_button.setEnabled(True)

                        # إعادة تعيين أنماط الحقول إلى الحالة الطبيعية
                        self.reset_input_styles()
                    else:
                        self.generate_button.setEnabled(False)
        else:
            # إنشاء رسالة خطأ أكثر تفصيلاً
            error_msg = QMessageBox()
            error_msg.setIcon(QMessageBox.Critical)
            error_msg.setWindowTitle("خطأ في المعالجة")
            error_msg.setText(f"حدث خطأ أثناء المعالجة: {message}")

            # إضافة نصائح للمستخدم بناءً على نوع الخطأ
            if "لم يتم تحميل الفيديو الأساسي" in message:
                error_msg.setInformativeText("يرجى تحميل فيديو أولاً باستخدام أحد الخيارات التالية:\n"
                                           "1. إدخال رابط فيديو يوتيوب والنقر على زر 'تحميل الفيديوهات'\n"
                                           "2. تفعيل خيار 'استخدام ملف محلي' واختيار ملف فيديو من جهازك")
            elif "الملف الأساسي غير موجود" in message:
                error_msg.setInformativeText("تم حذف أو نقل ملف الفيديو الأصلي. يرجى تحميل الفيديو مرة أخرى.")
            elif "لا يمكن فتح ملف الفيديو" in message:
                error_msg.setInformativeText("الملف المحدد ليس ملف فيديو صالح أو أنه تالف. يرجى تحميل ملف فيديو آخر.")
            elif "ملف الفيديو فارغ أو تالف" in message:
                error_msg.setInformativeText("ملف الفيديو فارغ أو تالف. يرجى تحميل ملف فيديو آخر.")
            elif "ffmpeg" in message.lower():
                error_msg.setInformativeText("هناك مشكلة في استخدام ffmpeg. تأكد من تثبيت ffmpeg بشكل صحيح على جهازك.")
            else:
                error_msg.setInformativeText("حاول مرة أخرى أو استخدم ملفًا مختلفًا. إذا استمرت المشكلة، أعد تشغيل التطبيق.")

            error_msg.setDetailedText(f"تفاصيل الخطأ: {message}")
            error_msg.setStandardButtons(QMessageBox.Ok)
            error_msg.exec_()

    def open_output_folder(self):
        """Open the output folder in file explorer."""
        try:
            # Open the output folder in file explorer
            import subprocess
            subprocess.Popen(f'explorer "{self.output_dir}"')
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء فتح المجلد: {str(e)}")

    def select_local_file(self):
        """Open file dialog to select a local video or audio file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار ملف فيديو أو صوت",
            "",
            "ملفات الفيديو (*.mp4 *.avi *.mkv *.mov);;ملفات الصوت (*.mp3 *.m4a *.wav);;جميع الملفات (*)"
        )

        if file_path:
            print(f"تم اختيار الملف: {file_path}")
            self.local_file_path = file_path
            self.use_local_file_checkbox.setChecked(True)

            # Update the file path display
            self.local_file_display.setText(file_path)
            self.local_file_display.setToolTip(file_path)

            # Determine if it's a video or audio file
            ext = os.path.splitext(file_path)[1].lower()
            file_type = ""

            if ext in ['.mp4', '.avi', '.mkv', '.mov']:
                print(f"تم تحديد ملف فيديو: {file_path}")
                self.video_file = file_path
                file_type = "فيديو"

                # التحقق من صلاحية ملف الفيديو
                try:
                    import cv2
                    cap = cv2.VideoCapture(file_path)
                    if not cap.isOpened():
                        print(f"لا يمكن فتح ملف الفيديو: {file_path}")
                        QMessageBox.warning(
                            self,
                            "تحذير",
                            f"لا يمكن فتح ملف الفيديو: {os.path.basename(file_path)}\n\n"
                            f"تأكد من أن الملف هو ملف فيديو صالح وأنه غير تالف."
                        )
                        self.video_file = None
                        self.generate_button.setEnabled(False)
                        cap.release()
                        return

                    # التحقق من مدة الفيديو
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    duration = frame_count / fps if fps > 0 else 0

                    if fps <= 0 or frame_count <= 0:
                        print(f"ملف الفيديو فارغ أو تالف: {file_path}")
                        QMessageBox.warning(
                            self,
                            "تحذير",
                            f"ملف الفيديو فارغ أو تالف: {os.path.basename(file_path)}\n\n"
                            f"الرجاء اختيار ملف فيديو آخر."
                        )
                        self.video_file = None
                        self.generate_button.setEnabled(False)
                        cap.release()
                        return

                    # الفيديو صالح
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    print(f"معلومات الفيديو: {width}x{height}, {fps} fps, {duration:.2f} ثانية")
                    cap.release()

                    # تفعيل زر التوليد
                    self.generate_button.setEnabled(True)

                    # Set background color to indicate video file
                    self.local_file_display.setStyleSheet("""
                        QLineEdit {
                            background-color: #e6f7ff;
                            border: 1px solid #91d5ff;
                            color: #0050b3;
                            padding: 8px;
                            border-radius: 6px;
                        }
                    """)

                except Exception as e:
                    print(f"خطأ في التحقق من ملف الفيديو: {str(e)}")
                    QMessageBox.warning(
                        self,
                        "خطأ",
                        f"حدث خطأ أثناء التحقق من ملف الفيديو: {str(e)}\n\n"
                        f"تأكد من تثبيت OpenCV بشكل صحيح وأن الملف هو ملف فيديو صالح."
                    )
                    self.video_file = None
                    self.generate_button.setEnabled(False)
                    return

            elif ext in ['.mp3', '.m4a', '.wav']:
                print(f"تم تحديد ملف صوت: {file_path}")
                self.audio_file = file_path
                file_type = "صوت"

                # Set background color to indicate audio file
                self.local_file_display.setStyleSheet("""
                    QLineEdit {
                        background-color: #f9f0ff;
                        border: 1px solid #d3adf7;
                        color: #722ed1;
                        padding: 8px;
                        border-radius: 6px;
                    }
                """)
            else:
                print(f"نوع ملف غير معروف: {ext}")
                QMessageBox.warning(
                    self,
                    "تحذير",
                    f"نوع الملف غير مدعوم: {os.path.basename(file_path)}\n\n"
                    f"الرجاء اختيار ملف فيديو (mp4, avi, mkv, mov) أو ملف صوت (mp3, m4a, wav)."
                )
                return

            # Show a more elegant notification
            QMessageBox.information(
                self,
                "تم اختيار الملف",
                f"تم اختيار ملف {file_type}: {os.path.basename(file_path)}\n\n"
                f"يمكنك الآن المتابعة مع العمليات المناسبة لهذا النوع من الملفات."
            )



    def reload_video(self):
        """إعادة تحميل الفيديو الحالي أو تحميل فيديو جديد."""
        # التحقق من وجود رابط فيديو
        video_url = self.video_url_input.text().strip()

        if video_url:
            # إعادة تحميل الفيديو من الرابط
            self.download_videos()
            return

        # التحقق من وجود ملف محلي
        if self.use_local_file_checkbox.isChecked():
            if self.local_file_path:
                # إعادة تحميل الملف المحلي
                self.video_file = self.local_file_path
                self.check_video_status()
                return
            else:
                # فتح مربع حوار لاختيار ملف محلي
                self.select_local_file()
                return

        # لا يوجد فيديو محدد
        error_msg = QMessageBox()
        error_msg.setIcon(QMessageBox.Warning)
        error_msg.setWindowTitle("تنبيه")
        error_msg.setText("لم يتم تحديد فيديو للتحميل.")
        error_msg.setInformativeText("يرجى إدخال رابط فيديو يوتيوب أو اختيار ملف محلي.")
        error_msg.setStandardButtons(QMessageBox.Ok)
        error_msg.exec_()

    def select_audio_file(self):
        """فتح مربع حوار لاختيار ملف صوتي محلي."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار ملف صوت",
            "",
            "ملفات الصوت (*.mp3 *.wav *.m4a *.aac *.ogg);;جميع الملفات (*)"
        )

        if file_path:
            self.custom_audio_file = file_path
            self.audio_file_path.setText(file_path)
            self.audio_file_path.setToolTip(file_path)
            self.audio_checkbox.setChecked(True)

            # تغيير لون خلفية حقل الإدخال للإشارة إلى أن ملف الصوت تم اختياره
            self.audio_file_path.setStyleSheet("""
                QLineEdit {
                    background-color: #f9f0ff;
                    border: 1px solid #d3adf7;
                    color: #722ed1;
                    padding: 8px;
                    border-radius: 6px;
                }
            """)

            QMessageBox.information(
                self,
                "تم اختيار ملف الصوت",
                f"تم اختيار ملف الصوت: {os.path.basename(file_path)}\n\n"
                f"سيتم استخدام هذا الملف عند توليد المقاطع."
            )

    def toggle_audio_start_time(self, state):
        """تفعيل أو تعطيل حقل بداية الصوت بناءً على حالة مربع الاختيار."""
        self.audio_start_minute_input.setEnabled(not state)

        # إذا تم تفعيل "استخدام نفس نقطة بداية الفيديو"، قم بتحديث قيمة بداية الصوت
        if state:
            self.audio_start_minute_input.setValue(self.start_minute_input.value())

    def update_audio_volume(self, value):
        """تحديث قيمة مستوى الصوت."""
        self.audio_volume_label.setText(f"{value}%")
        self.audio_volume = value / 100.0  # تحويل النسبة المئوية إلى قيمة عشرية

    def calculate_possible_segments(self):
        """حساب عدد المقاطع الممكنة بناءً على مدة الصوت المتوفر."""
        # التحقق من وجود ملف صوت
        audio_file = None

        # تحديد ملف الصوت المستخدم
        if self.audio_file:
            audio_file = self.audio_file
        elif self.custom_audio_file:
            audio_file = self.custom_audio_file
        elif self.video_file:
            # إذا كان هناك ملف فيديو، نستخرج الصوت منه مؤقتًا
            try:
                QMessageBox.information(
                    self,
                    "جاري استخراج الصوت",
                    "لم يتم تحديد ملف صوت. سيتم استخراج الصوت من ملف الفيديو لحساب المدة."
                )
                audio_file = self.video_processor.extract_audio(self.video_file)
            except Exception as e:
                QMessageBox.warning(
                    self,
                    "خطأ",
                    f"فشل في استخراج الصوت من الفيديو: {str(e)}"
                )
                return

        if not audio_file:
            QMessageBox.warning(
                self,
                "تنبيه",
                "يرجى تحميل ملف صوت أو فيديو أولاً"
            )
            return

        # الحصول على المعلمات
        segment_duration = self.duration_input.value()  # مدة المقطع بالثواني
        start_minute = self.start_minute_input.value()  # نقطة البداية بالدقائق
        interval = self.interval_input.value()  # الفاصل بين المقاطع بالثواني
        sequential_mode = self.sequential_checkbox.isChecked()  # وضع المقاطع المتتالية

        # تحويل نقطة البداية إلى ثواني
        start_seconds = start_minute * 60

        try:
            # الحصول على مدة الصوت باستخدام ffmpeg
            import subprocess

            # التحقق من وجود ffmpeg
            try:
                result = subprocess.run(['ffmpeg', '-version'],
                                      stdout=subprocess.PIPE,
                                      stderr=subprocess.PIPE,
                                      text=True,
                                      check=False)
                if result.returncode != 0:
                    QMessageBox.warning(
                        self,
                        "تنبيه",
                        "برنامج ffmpeg غير متوفر. يرجى تثبيته لتمكين حساب مدة الصوت."
                    )
                    return
            except:
                QMessageBox.warning(
                    self,
                    "تنبيه",
                    "برنامج ffmpeg غير متوفر. يرجى تثبيته لتمكين حساب مدة الصوت."
                )
                return

            # استخدام ffprobe للحصول على مدة الصوت
            cmd = [
                'ffprobe',
                '-v', 'error',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                audio_file
            ]

            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            if result.returncode != 0:
                QMessageBox.warning(
                    self,
                    "خطأ",
                    f"فشل في الحصول على مدة الصوت: {result.stderr}"
                )
                return

            total_duration = float(result.stdout.strip())  # المدة الكلية بالثواني

            # حساب المدة المتبقية بعد نقطة البداية
            remaining_duration = total_duration - start_seconds

            if remaining_duration <= 0:
                QMessageBox.warning(
                    self,
                    "تنبيه",
                    f"نقطة البداية ({start_minute} دقيقة) تتجاوز مدة الصوت ({total_duration/60:.1f} دقيقة)"
                )
                return

            # حساب عدد المقاطع الممكنة
            if sequential_mode:
                # في الوضع المتسلسل، المقاطع متتالية بدون فواصل
                possible_segments = int(remaining_duration // segment_duration)
            else:
                # في وضع الفواصل، يجب مراعاة الفاصل بين المقاطع
                if interval >= 0:
                    # حساب عدد المقاطع مع مراعاة الفواصل
                    possible_segments = int((remaining_duration + interval) // (segment_duration + interval))
                else:
                    # إذا كان الفاصل سالبًا، فهذا يعني تداخل المقاطع
                    possible_segments = int(remaining_duration // (segment_duration + interval))

            # عرض النتيجة
            self.segments_result_label.setText(f"عدد المقاطع الممكنة: {possible_segments}")

            # تحديث شريط التقدم
            max_segments = 100  # الحد الأقصى للمقاطع في شريط التقدم
            self.segments_progress_bar.setRange(0, max_segments)
            self.segments_progress_bar.setValue(min(possible_segments, max_segments))
            self.segments_progress_bar.setFormat(f"%v من {possible_segments} مقطع")

            # عرض معلومات إضافية
            info_message = (
                f"معلومات الصوت:\n"
                f"- المدة الكلية: {total_duration/60:.1f} دقيقة ({total_duration:.1f} ثانية)\n"
                f"- نقطة البداية: {start_minute} دقيقة ({start_seconds} ثانية)\n"
                f"- المدة المتبقية: {remaining_duration/60:.1f} دقيقة ({remaining_duration:.1f} ثانية)\n\n"
                f"إعدادات المقاطع:\n"
                f"- مدة المقطع: {segment_duration} ثانية\n"
                f"- الفاصل بين المقاطع: {interval} ثانية\n"
                f"- وضع المقاطع المتتالية: {'مفعل' if sequential_mode else 'غير مفعل'}\n\n"
                f"يمكن إنشاء {possible_segments} مقطع بناءً على هذه الإعدادات."
            )

            # إنشاء رسالة معلومات مخصصة
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("نتيجة حساب المقاطع")
            msg_box.setText(info_message)
            msg_box.setIcon(QMessageBox.Information)

            # تعيين أنماط CSS للرسالة
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #FFFFFF;
                    font-size: 14px;
                }
                QLabel {
                    min-width: 500px;
                    max-width: 500px;
                    min-height: 200px;
                    color: #333333;
                    font-size: 14px;
                }
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    font-weight: bold;
                    padding: 8px 16px;
                    border-radius: 6px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #1E88E5;
                }
                QPushButton:pressed {
                    background-color: #1565C0;
                }
            """)

            # عرض الرسالة
            msg_box.exec_()

            # تحديث قيمة عدد المقاطع في حقل الإدخال
            self.count_input.setValue(min(possible_segments, self.count_input.maximum()))

        except Exception as e:
            # إنشاء رسالة خطأ مخصصة
            error_box = QMessageBox(self)
            error_box.setWindowTitle("خطأ")
            error_box.setText("حدث خطأ أثناء حساب عدد المقاطع")
            error_box.setInformativeText(str(e))
            error_box.setIcon(QMessageBox.Warning)

            # تعيين أنماط CSS للرسالة
            error_box.setStyleSheet("""
                QMessageBox {
                    background-color: #FFFFFF;
                    font-size: 14px;
                }
                QLabel {
                    min-width: 400px;
                    color: #333333;
                    font-size: 14px;
                }
                QPushButton {
                    background-color: #F44336;
                    color: white;
                    font-weight: bold;
                    padding: 8px 16px;
                    border-radius: 6px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #E53935;
                }
                QPushButton:pressed {
                    background-color: #D32F2F;
                }
            """)

            # عرض الرسالة
            error_box.exec_()

    def clean_temp_files(self):
        """تنظيف الملفات المؤقتة من مجلد المخرجات."""
        try:
            print("تنظيف الملفات المؤقتة عند بدء التطبيق...")
            deleted_count = 0
            failed_count = 0

            # تحقق من وجود مجلد المخرجات
            if not os.path.exists(self.output_dir):
                return

            # قائمة بأنماط الملفات المؤقتة التي يجب حذفها
            temp_patterns = [
                "temp_", ".tmp", "segment_*_step1_*", "segment_*_step2_*",
                "segment_*_step3_*", "segment_*_with_effect", "segment_*_portrait"
            ]

            # البحث عن الملفات المؤقتة وحذفها
            for filename in os.listdir(self.output_dir):
                file_path = os.path.join(self.output_dir, filename)

                # تخطي المجلدات
                if os.path.isdir(file_path):
                    continue

                # تحقق مما إذا كان الملف يطابق أحد أنماط الملفات المؤقتة
                is_temp = False
                for pattern in temp_patterns:
                    if "*" in pattern:
                        # استخدام مطابقة بسيطة للأنماط
                        pattern_parts = pattern.split("*")
                        if len(pattern_parts) == 2:
                            if filename.startswith(pattern_parts[0]) and filename.endswith(pattern_parts[1]):
                                is_temp = True
                                break
                        elif pattern.startswith("*") and filename.endswith(pattern[1:]):
                            is_temp = True
                            break
                        elif pattern.endswith("*") and filename.startswith(pattern[:-1]):
                            is_temp = True
                            break
                    elif pattern in filename:
                        is_temp = True
                        break

                # حذف الملف إذا كان مؤقتًا
                if is_temp:
                    try:
                        # تحقق من أن الملف غير مستخدم حاليًا
                        try:
                            with open(file_path, 'a'):
                                pass
                        except:
                            print(f"تخطي الملف المستخدم حاليًا: {filename}")
                            continue

                        print(f"حذف الملف المؤقت: {filename}")
                        os.remove(file_path)
                        deleted_count += 1
                    except Exception as e:
                        print(f"فشل في حذف الملف المؤقت: {filename} - {str(e)}")
                        failed_count += 1

            if deleted_count > 0:
                print(f"تم حذف {deleted_count} ملف مؤقت عند بدء التطبيق")
            if failed_count > 0:
                print(f"فشل في حذف {failed_count} ملف مؤقت")

        except Exception as e:
            print(f"خطأ في تنظيف الملفات المؤقتة: {str(e)}")

    def check_video_status(self):
        """التحقق من حالة الفيديو وإعادة تهيئة المتغيرات إذا لزم الأمر."""
        # التحقق من وجود ملف فيديو مخزن
        if self.video_file:
            # التحقق من وجود الملف على القرص
            if not os.path.exists(self.video_file):
                print(f"ملف الفيديو غير موجود على القرص: {self.video_file}")
                self.video_file = None
                self.generate_button.setEnabled(False)
                return

            # التحقق من أن الملف هو فيديو صالح
            try:
                import cv2
                cap = cv2.VideoCapture(self.video_file)
                if not cap.isOpened():
                    print(f"لا يمكن فتح ملف الفيديو: {self.video_file}")
                    self.video_file = None
                    self.generate_button.setEnabled(False)
                    cap.release()
                    return

                # التحقق من مدة الفيديو
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                if fps <= 0 or frame_count <= 0:
                    print(f"ملف الفيديو فارغ أو تالف: {self.video_file}")
                    self.video_file = None
                    self.generate_button.setEnabled(False)
                    cap.release()
                    return

                # الفيديو صالح
                print(f"تم التحقق من صلاحية الفيديو: {self.video_file}")
                self.generate_button.setEnabled(True)

                # عرض معلومات الفيديو في حقل الملف المحلي
                if self.use_local_file_checkbox.isChecked():
                    self.local_file_display.setText(self.video_file)
                    self.local_file_display.setToolTip(self.video_file)

                    # تغيير لون خلفية حقل الإدخال للإشارة إلى أن ملف الفيديو صالح
                    self.local_file_display.setStyleSheet("""
                        QLineEdit {
                            background-color: #e6f7ff;
                            border: 1px solid #91d5ff;
                            color: #0050b3;
                            padding: 8px;
                            border-radius: 6px;
                        }
                    """)

                cap.release()
            except Exception as e:
                print(f"خطأ في التحقق من ملف الفيديو: {str(e)}")
                self.video_file = None
                self.generate_button.setEnabled(False)
        else:
            # لا يوجد ملف فيديو مخزن
            self.generate_button.setEnabled(False)

    def reset_input_styles(self):
        """إعادة تعيين أنماط حقول الإدخال إلى الحالة الطبيعية."""
        # إعادة تعيين نمط حقل إدخال رابط الفيديو
        self.video_url_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 6px;
                background-color: white;
                min-height: 30px;
                min-width: 200px;
            }
            QLineEdit:focus {
                border: 1px solid #4a86e8;
            }
            QLineEdit:hover {
                border: 1px solid #bbb;
            }
        """)

        # إعادة تعيين نمط مربع تحديد استخدام ملف محلي
        self.use_local_file_checkbox.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                min-height: 25px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 1px solid #ccc;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #4a86e8;
                background-color: #4a86e8;
                border-radius: 3px;
            }
        """)

    def closeEvent(self, event):
        """تنظيف الملفات المؤقتة عند إغلاق التطبيق."""
        try:
            # سؤال المستخدم إذا كان يريد تنظيف الملفات المؤقتة
            reply = QMessageBox.question(
                self,
                "تنظيف الملفات المؤقتة",
                "هل ترغب في تنظيف الملفات المؤقتة قبل الخروج؟\n\n"
                "سيؤدي هذا إلى حذف جميع الملفات الوسيطة والمؤقتة من مجلد المخرجات.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # تنظيف الملفات المؤقتة
                self.clean_temp_files()

                # عرض رسالة تأكيد
                QMessageBox.information(
                    self,
                    "تم التنظيف",
                    "تم تنظيف الملفات المؤقتة بنجاح."
                )
        except Exception as e:
            print(f"خطأ في تنظيف الملفات المؤقتة عند الإغلاق: {str(e)}")

        # استمرار في إغلاق التطبيق
        event.accept()


