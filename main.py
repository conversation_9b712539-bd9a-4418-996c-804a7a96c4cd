"""
Main entry point for MekaVido application.
"""
import os
import sys
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("mekavido.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("MekaVido")

def main():
    """Main application entry point."""
    try:
        # Add the current directory to the path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)

        # Enable high DPI scaling before creating QApplication
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        app = QApplication(sys.argv)

        # Set application style
        app.setStyle("Fusion")

        # Import the main window after setting the path
        from ui.main_window import MainWindow

        # Create and show the main window
        window = MainWindow()
        window.show()

        # Start the application event loop
        sys.exit(app.exec_())

    except Exception as e:
        logger.error(f"Error starting application: {str(e)}")
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
