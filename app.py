"""
Main application file for MekaVido.
"""
import os
import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QThread, pyqtSignal
import logging

# Import custom modules
from ui.main_window import MainWindow
from .video_processor import VideoProcessor
from .audio_processor import AudioProcessor
from .subtitle_generator import SubtitleGenerator
from .utils.download import VideoDownloader
from .speech_recognition_module import SpeechRecognizer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("mekavido.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("MekaVido")

class VideoProcessingWorker(QThread):
    """Worker thread for video processing operations."""
    progress_updated = pyqtSignal(int)
    processing_completed = pyqtSignal(bool, str, list)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.task = None
        self.params = None

        # Initialize processors
        self.output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output")
        os.makedirs(self.output_dir, exist_ok=True)

        self.video_processor = VideoProcessor(self.output_dir)
        self.audio_processor = AudioProcessor(self.output_dir)
        self.subtitle_generator = SubtitleGenerator(self.output_dir)
        self.downloader = VideoDownloader(self.output_dir)
        self.speech_recognizer = SpeechRecognizer(self.output_dir)

        # Store downloaded files
        self.video_file = None
        self.audio_file = None
        self.generated_files = []

        # Store recognized text
        self.recognized_text = None

    def setup_task(self, task, params):
        """Set up the task and parameters."""
        self.task = task
        self.params = params

    def run(self):
        """Run the task."""
        try:
            if self.task == 'download':
                self._download_videos()
            elif self.task == 'generate':
                self._generate_videos()
            elif self.task == 'recognize_speech':
                self._recognize_speech()
        except Exception as e:
            logger.error(f"Error in processing thread: {str(e)}")
            self.processing_completed.emit(False, str(e), [])

    def _download_videos(self):
        """Download videos from the provided URLs."""
        video_url = self.params.get('video_url')
        audio_url = self.params.get('audio_url')

        try:
            # Download main video
            logger.info(f"Downloading main video from: {video_url}")
            self.progress_updated.emit(10)

            video_file, error = self.downloader.download_with_ytdlp(video_url)
            if error:
                raise Exception(f"Error downloading main video: {error}")

            self.video_file = video_file
            self.progress_updated.emit(50)

            # Download audio video if provided
            if audio_url:
                logger.info(f"Downloading audio video from: {audio_url}")
                audio_file, error = self.downloader.download_with_ytdlp(audio_url)
                if error:
                    raise Exception(f"Error downloading audio video: {error}")

                # Extract audio from the audio video
                self.audio_file = self.audio_processor.extract_audio_from_video(audio_file)

            self.progress_updated.emit(100)
            self.processing_completed.emit(True, "تم تحميل الفيديوهات بنجاح", [self.video_file, self.audio_file])

        except Exception as e:
            logger.error(f"Error downloading videos: {str(e)}")
            self.processing_completed.emit(False, str(e), [])

    def _generate_videos(self):
        """Generate video segments with the specified settings."""
        if not self.video_file:
            self.processing_completed.emit(False, "لم يتم تحميل الفيديو الأساسي", [])
            return

        try:
            duration = self.params.get('duration', 15)
            count = self.params.get('count', 5)
            effect = self.params.get('effect', "بدون مؤثرات")
            generate_captions = self.params.get('generate_captions', True)

            # Step 1: Cut video into segments
            logger.info(f"Cutting video into {count} segments of {duration} seconds each")
            self.progress_updated.emit(10)

            segments = self.video_processor.cut_video_into_segments(
                self.video_file, duration, count
            )

            self.progress_updated.emit(30)

            # Step 2: Process each segment
            processed_segments = []

            for i, segment in enumerate(segments):
                logger.info(f"Processing segment {i+1}/{len(segments)}")

                # Apply effects if selected
                if effect != "بدون مؤثرات":
                    effects_map = {
                        "أبيض وأسود": ["black_white"],
                        "زيادة السطوع": ["bright"],
                        "زيادة التباين": ["contrast"],
                        "عكس الفيديو": ["mirror"]
                    }

                    if effect in effects_map:
                        segment = self.video_processor.apply_effects(
                            segment, effects_map[effect], f"segment_{i+1}_with_effect"
                        )

                # If audio file is available, merge it with the segment
                if self.audio_file:
                    # Trim audio to match segment duration
                    trimmed_audio = self.audio_processor.trim_audio(
                        self.audio_file, 0, duration
                    )

                    # Merge audio with video
                    segment = self.video_processor.merge_audio_with_video(
                        segment, trimmed_audio, f"segment_{i+1}_with_audio"
                    )

                # Generate captions if selected
                if generate_captions and self.audio_file:
                    # Extract audio from the segment
                    segment_audio = self.audio_processor.extract_audio_from_video(segment)

                    # Generate timed captions
                    captions = self.audio_processor.generate_timed_captions(
                        segment_audio, "ar-AR", 3
                    )

                    # Add captions to the video
                    if captions:
                        segment = self.subtitle_generator.add_subtitles_to_video(
                            segment, captions, f"segment_{i+1}_final"
                        )

                processed_segments.append(segment)
                self.progress_updated.emit(30 + (i + 1) * 60 // len(segments))

            self.generated_files = processed_segments
            self.progress_updated.emit(100)
            self.processing_completed.emit(
                True,
                f"تم إنشاء {len(processed_segments)} مقاطع بنجاح",
                processed_segments
            )

        except Exception as e:
            logger.error(f"Error generating videos: {str(e)}")
            self.processing_completed.emit(False, str(e), [])

    def _recognize_speech(self):
        """Recognize speech from the audio file."""
        # استخدام ملف الصوت المحدد من المعلمات إذا كان متوفرًا
        audio_file = self.params.get('audio_file', self.audio_file)

        if not audio_file:
            self.processing_completed.emit(False, "لم يتم تحميل ملف صوتي", [])
            return

        try:
            logger.info(f"Recognizing speech from audio file: {audio_file}")
            self.progress_updated.emit(10)

            # Recognize speech
            language = self.params.get('language', 'ar')
            self.recognized_text = self.speech_recognizer.recognize_speech(
                audio_file, language=language
            )

            self.progress_updated.emit(100)

            if self.recognized_text:
                self.processing_completed.emit(
                    True,
                    "تم التعرف على الكلام بنجاح",
                    [self.recognized_text]
                )
            else:
                self.processing_completed.emit(
                    False,
                    "فشل في التعرف على الكلام",
                    []
                )

        except Exception as e:
            logger.error(f"Error recognizing speech: {str(e)}")
            self.processing_completed.emit(False, str(e), [])


def main():
    """Main application entry point."""
    # Enable high DPI scaling
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)

    # Set application style
    app.setStyle("Fusion")

    # Create and show the main window
    window = MainWindow()
    window.show()

    # Start the application event loop
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
