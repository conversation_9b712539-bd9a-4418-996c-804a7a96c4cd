"""
Audio processing module for MekaVido application.
"""
import os
import speech_recognition as sr
from moviepy.editor import AudioFileClip
from typing import List, Tuple

class AudioProcessor:
    """Class for processing audio and generating transcriptions."""

    def __init__(self, output_dir="./output"):
        """Initialize the processor with output directory."""
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.recognizer = sr.Recognizer()

    def extract_audio_from_video(self, video_path: str) -> str:
        """
        Extract audio from a video file.

        Args:
            video_path (str): Path to the video file

        Returns:
            str: Path to the extracted audio file
        """
        try:
            print(f"Extracting audio from video: {video_path}")

            # Check if the video file exists
            if not os.path.exists(video_path):
                print(f"Video file does not exist: {video_path}")
                return None

            # Generate output file path
            base_name = os.path.splitext(os.path.basename(video_path))[0]
            output_file = os.path.join(self.output_dir, f"{base_name}_audio.wav")

            # Make sure output directory exists
            os.makedirs(self.output_dir, exist_ok=True)

            # Extract audio using moviepy
            print(f"Creating AudioFileClip from {video_path}")
            video_clip = AudioFileClip(video_path)

            print(f"Writing audio to {output_file}")
            video_clip.write_audiofile(output_file, verbose=True)
            video_clip.close()

            # Verify the output file exists
            if os.path.exists(output_file):
                print(f"Audio extraction successful: {output_file}")
                return output_file
            else:
                print(f"Audio file was not created: {output_file}")
                return None

        except Exception as e:
            import traceback
            print(f"Error extracting audio: {str(e)}")
            print(traceback.format_exc())
            return None

    def transcribe_audio(self, audio_path: str, language: str = "ar-AR") -> str:
        """
        Transcribe audio to text.

        Args:
            audio_path (str): Path to the audio file
            language (str): Language code for transcription

        Returns:
            str: Transcribed text
        """
        try:
            with sr.AudioFile(audio_path) as source:
                audio_data = self.recognizer.record(source)
                text = self.recognizer.recognize_google(audio_data, language=language)
                return text

        except sr.UnknownValueError:
            return "Speech Recognition could not understand audio"
        except sr.RequestError as e:
            return f"Could not request results from Speech Recognition service; {e}"
        except Exception as e:
            print(f"Error transcribing audio: {str(e)}")
            return None

    def generate_timed_captions(self, audio_path: str, language: str = "ar-AR",
                               segment_duration: int = 5) -> List[Tuple[float, float, str]]:
        """
        Generate timed captions from audio.

        Args:
            audio_path (str): Path to the audio file
            language (str): Language code for transcription
            segment_duration (int): Duration of each segment in seconds

        Returns:
            List[Tuple[float, float, str]]: List of (start_time, end_time, text) tuples
        """
        try:
            audio_clip = AudioFileClip(audio_path)
            total_duration = audio_clip.duration

            # Create temporary segments for transcription
            captions = []
            current_time = 0

            while current_time < total_duration:
                end_time = min(current_time + segment_duration, total_duration)

                # Extract segment
                segment = audio_clip.subclip(current_time, end_time)
                temp_file = os.path.join(self.output_dir, "temp_segment.wav")
                segment.write_audiofile(temp_file, verbose=False, logger=None)

                # Transcribe segment
                try:
                    with sr.AudioFile(temp_file) as source:
                        audio_data = self.recognizer.record(source)
                        text = self.recognizer.recognize_google(audio_data, language=language)

                        if text:
                            captions.append((current_time, end_time, text))
                except:
                    # If transcription fails, just continue
                    pass

                # Clean up
                os.remove(temp_file)
                current_time = end_time

            # Close the audio clip
            audio_clip.close()

            return captions

        except Exception as e:
            print(f"Error generating timed captions: {str(e)}")
            return []

    def adjust_audio_volume(self, audio_path: str, volume_factor: float = 1.5) -> str:
        """
        Adjust the volume of an audio file.

        Args:
            audio_path (str): Path to the audio file
            volume_factor (float): Factor to multiply the volume by

        Returns:
            str: Path to the adjusted audio file
        """
        try:
            # Generate output file path
            base_name = os.path.splitext(os.path.basename(audio_path))[0]
            output_file = os.path.join(self.output_dir, f"{base_name}_adjusted.mp3")

            # Adjust volume using moviepy
            audio_clip = AudioFileClip(audio_path)
            audio_clip = audio_clip.volumex(volume_factor)
            audio_clip.write_audiofile(output_file)
            audio_clip.close()

            return output_file

        except Exception as e:
            print(f"Error adjusting audio volume: {str(e)}")
            return None

    def trim_audio(self, audio_path: str, start_time: float, end_time: float) -> str:
        """
        Trim an audio file to a specific duration.

        Args:
            audio_path (str): Path to the audio file
            start_time (float): Start time in seconds
            end_time (float): End time in seconds

        Returns:
            str: Path to the trimmed audio file
        """
        try:
            # Generate output file path
            base_name = os.path.splitext(os.path.basename(audio_path))[0]
            output_file = os.path.join(self.output_dir, f"{base_name}_trimmed.mp3")

            # Trim audio using moviepy
            audio_clip = AudioFileClip(audio_path)
            trimmed_clip = audio_clip.subclip(start_time, end_time)
            trimmed_clip.write_audiofile(output_file)

            # Close the clips
            trimmed_clip.close()
            audio_clip.close()

            return output_file

        except Exception as e:
            print(f"Error trimming audio: {str(e)}")
            return None
