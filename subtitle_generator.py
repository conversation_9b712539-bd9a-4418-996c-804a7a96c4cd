"""
Subtitle generation module for MekaVido application.
"""
import os
from typing import List, <PERSON><PERSON>
import speech_recognition as sr
from moviepy.editor import TextClip, CompositeVideoClip, VideoFileClip

class SubtitleGenerator:
    """Class for generating and adding subtitles to videos."""
    
    def __init__(self, output_dir="./output"):
        """Initialize the generator with output directory."""
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.recognizer = sr.Recognizer()
    
    def generate_srt_file(self, captions: List[Tuple[float, float, str]], output_name: str) -> str:
        """
        Generate an SRT subtitle file from captions.
        
        Args:
            captions (List[Tuple[float, float, str]]): List of (start_time, end_time, text) tuples
            output_name (str): Name for the output file
            
        Returns:
            str: Path to the generated SRT file
        """
        try:
            output_file = os.path.join(self.output_dir, f"{output_name}.srt")
            
            with open(output_file, 'w', encoding='utf-8') as f:
                for i, (start, end, text) in enumerate(captions, 1):
                    # Convert seconds to SRT format (HH:MM:SS,mmm)
                    start_str = self._seconds_to_srt_time(start)
                    end_str = self._seconds_to_srt_time(end)
                    
                    # Write the subtitle entry
                    f.write(f"{i}\n")
                    f.write(f"{start_str} --> {end_str}\n")
                    f.write(f"{text}\n\n")
            
            return output_file
            
        except Exception as e:
            print(f"Error generating SRT file: {str(e)}")
            return None
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """
        Convert seconds to SRT time format (HH:MM:SS,mmm).
        
        Args:
            seconds (float): Time in seconds
            
        Returns:
            str: Time in SRT format
        """
        hours = int(seconds // 3600)
        seconds %= 3600
        minutes = int(seconds // 60)
        seconds %= 60
        milliseconds = int((seconds - int(seconds)) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"
    
    def add_subtitles_to_video(self, video_path: str, captions: List[Tuple[float, float, str]], 
                              output_name: str = None, font_size: int = 40, 
                              font_color: str = 'white', bg_color: str = 'black') -> str:
        """
        Add subtitles directly to a video.
        
        Args:
            video_path (str): Path to the video file
            captions (List[Tuple[float, float, str]]): List of (start_time, end_time, text) tuples
            output_name (str, optional): Name for the output file
            font_size (int): Font size for subtitles
            font_color (str): Font color for subtitles
            bg_color (str): Background color for subtitles
            
        Returns:
            str: Path to the video with subtitles
        """
        try:
            video = VideoFileClip(video_path)
            
            # Create TextClips for each caption
            subtitle_clips = []
            
            for start_time, end_time, text in captions:
                txt_clip = TextClip(
                    text, 
                    fontsize=font_size, 
                    color=font_color,
                    bg_color=bg_color,
                    font='Arial',
                    method='caption',
                    align='center',
                    size=(video.w * 0.8, None)
                )
                
                # Position at the bottom of the frame
                txt_clip = txt_clip.set_position(('center', 'bottom'))
                txt_clip = txt_clip.set_start(start_time).set_end(end_time)
                subtitle_clips.append(txt_clip)
            
            # Add all subtitle clips to the video
            final_video = CompositeVideoClip([video] + subtitle_clips)
            
            # Generate output file path
            if output_name:
                output_file = os.path.join(self.output_dir, f"{output_name}.mp4")
            else:
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_file = os.path.join(self.output_dir, f"{base_name}_with_subtitles.mp4")
            
            # Write the result to a file
            final_video.write_videofile(output_file, codec="libx264", audio_codec="aac")
            
            # Close the videos
            final_video.close()
            video.close()
            
            return output_file
            
        except Exception as e:
            print(f"Error adding subtitles to video: {str(e)}")
            return None
    
    def create_styled_subtitles(self, video_path: str, captions: List[Tuple[float, float, str]], 
                               output_name: str = None, font_size: int = 40, 
                               font_color: str = 'white', bg_color: str = 'black',
                               position: str = 'bottom') -> str:
        """
        Create styled subtitles for a video.
        
        Args:
            video_path (str): Path to the video file
            captions (List[Tuple[float, float, str]]): List of (start_time, end_time, text) tuples
            output_name (str, optional): Name for the output file
            font_size (int): Font size for subtitles
            font_color (str): Font color for subtitles
            bg_color (str): Background color for subtitles
            position (str): Position of subtitles ('bottom', 'top', 'center')
            
        Returns:
            str: Path to the video with styled subtitles
        """
        try:
            video = VideoFileClip(video_path)
            
            # Determine position coordinates
            if position == 'bottom':
                pos = ('center', 'bottom')
            elif position == 'top':
                pos = ('center', 'top')
            else:  # center
                pos = 'center'
            
            # Create TextClips for each caption
            subtitle_clips = []
            
            for start_time, end_time, text in captions:
                txt_clip = TextClip(
                    text, 
                    fontsize=font_size, 
                    color=font_color,
                    bg_color=bg_color,
                    font='Arial',
                    method='caption',
                    align='center',
                    size=(video.w * 0.8, None)
                )
                
                # Set position and timing
                txt_clip = txt_clip.set_position(pos)
                txt_clip = txt_clip.set_start(start_time).set_end(end_time)
                subtitle_clips.append(txt_clip)
            
            # Add all subtitle clips to the video
            final_video = CompositeVideoClip([video] + subtitle_clips)
            
            # Generate output file path
            if output_name:
                output_file = os.path.join(self.output_dir, f"{output_name}.mp4")
            else:
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_file = os.path.join(self.output_dir, f"{base_name}_with_styled_subtitles.mp4")
            
            # Write the result to a file
            final_video.write_videofile(output_file, codec="libx264", audio_codec="aac")
            
            # Close the videos
            final_video.close()
            video.close()
            
            return output_file
            
        except Exception as e:
            print(f"Error creating styled subtitles: {str(e)}")
            return None
