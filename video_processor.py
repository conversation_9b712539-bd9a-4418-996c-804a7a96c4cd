"""
Video processing module for MekaVido application.
"""
import os
import random
from typing import List, Tuple
from moviepy.editor import VideoFileClip, AudioFileClip, TextClip, CompositeVideoClip
from moviepy.video.fx import all as vfx

class VideoProcessor:
    """Class for processing videos."""

    def __init__(self, output_dir="./output"):
        """Initialize the processor with output directory."""
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

    def cut_video_into_segments(self, video_path: str, segment_duration: int,
                               num_segments: int) -> List[str]:
        """
        Cut a video into multiple segments of specified duration.

        Args:
            video_path (str): Path to the video file
            segment_duration (int): Duration of each segment in seconds
            num_segments (int): Number of segments to create

        Returns:
            List[str]: List of paths to the created segments
        """
        try:
            print(f"Cutting video into segments: {video_path}")
            print(f"Segment duration: {segment_duration} seconds")
            print(f"Number of segments: {num_segments}")

            # Check if the video file exists
            if not os.path.exists(video_path):
                print(f"Video file does not exist: {video_path}")
                return []

            # Make sure output directory exists
            os.makedirs(self.output_dir, exist_ok=True)

            # Load the video
            print(f"Loading video file: {video_path}")
            video = VideoFileClip(video_path)
            total_duration = video.duration
            print(f"Video duration: {total_duration} seconds")

            # Calculate how many segments we can create
            max_segments = int(total_duration // segment_duration)
            if max_segments < num_segments:
                print(f"Requested {num_segments} segments, but can only create {max_segments} segments")
                num_segments = max_segments

            if num_segments <= 0:
                print("Cannot create any segments (video too short)")
                video.close()
                return []

            # Generate random start times for segments
            segment_files = []
            used_starts = set()

            for i in range(num_segments):
                print(f"Creating segment {i+1}/{num_segments}")

                # Find a start time that doesn't overlap with previous segments
                attempts = 0
                max_attempts = 50

                while attempts < max_attempts:
                    max_start = total_duration - segment_duration
                    if max_start <= 0:
                        print("Video too short for segment")
                        break

                    start_time = random.uniform(0, max_start)

                    # Check if this start time overlaps with any previous segment
                    overlap = False
                    for used_start in used_starts:
                        if abs(used_start - start_time) < segment_duration:
                            overlap = True
                            break

                    if not overlap:
                        used_starts.add(start_time)
                        break

                    attempts += 1

                if attempts >= max_attempts:
                    print(f"Could not find non-overlapping segment after {max_attempts} attempts")
                    continue

                # Cut the segment
                print(f"Cutting segment from {start_time} to {start_time + segment_duration}")
                segment = video.subclip(start_time, start_time + segment_duration)

                # Save the segment
                output_file = os.path.join(self.output_dir, f"segment_{i+1}.mp4")
                print(f"Saving segment to {output_file}")
                segment.write_videofile(output_file, codec="libx264", audio_codec="aac")
                segment_files.append(output_file)

                # Close the segment to free memory
                segment.close()

            # Close the original video
            video.close()

            print(f"Created {len(segment_files)} segments")
            return segment_files

        except Exception as e:
            import traceback
            print(f"Error cutting video: {str(e)}")
            print(traceback.format_exc())
            return []

    def extract_audio(self, video_path: str) -> str:
        """
        Extract audio from a video file.

        Args:
            video_path (str): Path to the video file

        Returns:
            str: Path to the extracted audio file
        """
        try:
            video = VideoFileClip(video_path)
            audio = video.audio

            # Generate output file path
            base_name = os.path.splitext(os.path.basename(video_path))[0]
            output_file = os.path.join(self.output_dir, f"{base_name}_audio.mp3")

            # Write audio to file
            audio.write_audiofile(output_file)

            # Close the video and audio
            audio.close()
            video.close()

            return output_file

        except Exception as e:
            print(f"Error extracting audio: {str(e)}")
            return None

    def merge_audio_with_video(self, video_path: str, audio_path: str,
                              output_name: str = None) -> str:
        """
        Merge audio with a video file.

        Args:
            video_path (str): Path to the video file
            audio_path (str): Path to the audio file
            output_name (str, optional): Name for the output file

        Returns:
            str: Path to the merged video file
        """
        try:
            video = VideoFileClip(video_path)
            audio = AudioFileClip(audio_path)

            # Set the audio of the video
            video = video.set_audio(audio)

            # Generate output file path
            if output_name:
                output_file = os.path.join(self.output_dir, f"{output_name}.mp4")
            else:
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_file = os.path.join(self.output_dir, f"{base_name}_with_audio.mp4")

            # Write the result to a file
            video.write_videofile(output_file, codec="libx264", audio_codec="aac")

            # Close the video and audio
            audio.close()
            video.close()

            return output_file

        except Exception as e:
            print(f"Error merging audio with video: {str(e)}")
            return None

    def add_arabic_captions(self, video_path: str, captions: List[Tuple[float, float, str]],
                           output_name: str = None) -> str:
        """
        Add Arabic captions to a video.

        Args:
            video_path (str): Path to the video file
            captions (List[Tuple[float, float, str]]): List of (start_time, end_time, text) tuples
            output_name (str, optional): Name for the output file

        Returns:
            str: Path to the video with captions
        """
        try:
            video = VideoFileClip(video_path)

            # Create TextClips for each caption
            text_clips = []
            for start_time, end_time, text in captions:
                txt_clip = TextClip(
                    text,
                    fontsize=40,
                    color='white',
                    bg_color='black',
                    font='Arial',
                    method='caption',
                    align='center',
                    size=(video.w * 0.8, None)
                )

                # Position at the bottom of the frame
                txt_clip = txt_clip.set_position(('center', 'bottom')).set_start(start_time).set_end(end_time)
                text_clips.append(txt_clip)

            # Add all text clips to the video
            final_video = CompositeVideoClip([video] + text_clips)

            # Generate output file path
            if output_name:
                output_file = os.path.join(self.output_dir, f"{output_name}.mp4")
            else:
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_file = os.path.join(self.output_dir, f"{base_name}_with_captions.mp4")

            # Write the result to a file
            final_video.write_videofile(output_file, codec="libx264", audio_codec="aac")

            # Close the videos
            final_video.close()
            video.close()

            return output_file

        except Exception as e:
            print(f"Error adding captions: {str(e)}")
            return None

    def apply_effects(self, video_path: str, effects: List[str], output_name: str = None) -> str:
        """
        Apply simple effects to a video.

        Args:
            video_path (str): Path to the video file
            effects (List[str]): List of effects to apply ('mirror', 'black_white', 'bright', 'contrast')
            output_name (str, optional): Name for the output file

        Returns:
            str: Path to the video with effects
        """
        try:
            video = VideoFileClip(video_path)

            # Apply the requested effects
            for effect in effects:
                if effect == 'mirror':
                    video = video.fx(vfx.mirror_x)
                elif effect == 'black_white':
                    video = video.fx(vfx.blackwhite)
                elif effect == 'bright':
                    video = video.fx(vfx.colorx, 1.5)  # Increase brightness
                elif effect == 'contrast':
                    video = video.fx(vfx.lum_contrast, contrast=1.5)  # Increase contrast

            # Generate output file path
            if output_name:
                output_file = os.path.join(self.output_dir, f"{output_name}.mp4")
            else:
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_file = os.path.join(self.output_dir, f"{base_name}_with_effects.mp4")

            # Write the result to a file
            video.write_videofile(output_file, codec="libx264", audio_codec="aac")

            # Close the video
            video.close()

            return output_file

        except Exception as e:
            print(f"Error applying effects: {str(e)}")
            return None
