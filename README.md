# MekaVido - مقاطع فيديو قصيرة

تطبيق لإنشاء مقاطع فيديو قصيرة من فيديوهات يوتيوب مع إضافة الصوت والكابشنات العربية.

## المميزات

- تحميل فيديوهات من يوتيوب (فيديو أساسي وفيديو صوتي)
- تقطيع الفيديو إلى مقاطع قصيرة بمدة محددة
- دمج الصوت مع المقاطع
- توليد كابشنات عربية وإضافتها للفيديو
- تطبيق مؤثرات بسيطة على المقاطع
- واجهة مستخدم سهلة الاستخدام

## متطلبات النظام

- Python 3.8 أو أحدث
- المكتبات المذكورة في ملف requirements.txt

## التثبيت

1. قم بتثبيت Python من [الموقع الرسمي](https://www.python.org/downloads/)
2. قم بتنزيل أو استنساخ هذا المشروع
3. افتح موجه الأوامر (Command Prompt) في مجلد المشروع
4. قم بتثبيت المكتبات المطلوبة:

```
pip install -r requirements.txt
```

## طريقة الاستخدام

1. قم بتشغيل التطبيق:

```
python app.py
```

2. أدخل رابط الفيديو الأساسي (المرئي) من يوتيوب
3. أدخل رابط الفيديو الصوتي (اختياري)
4. اضغط على زر "تحميل الفيديوهات"
5. حدد مدة كل مقطع وعدد المقاطع المطلوبة
6. اختر المؤثرات المطلوبة (اختياري)
7. حدد ما إذا كنت تريد توليد كابشنات عربية
8. اضغط على زر "توليد المقاطع"
9. انتظر حتى اكتمال العملية
10. يمكنك الوصول إلى المقاطع المولدة في مجلد "output"

## ملاحظات هامة

- يتطلب التطبيق اتصالاً بالإنترنت لتحميل الفيديوهات من يوتيوب
- جودة الكابشنات العربية تعتمد على جودة الصوت في الفيديو الأصلي
- قد تستغرق عملية المعالجة وقتاً طويلاً اعتماداً على طول الفيديو وعدد المقاطع المطلوبة

## المساهمة

نرحب بمساهماتكم لتحسين هذا التطبيق! يمكنكم إرسال اقتراحاتكم وتعديلاتكم عبر Pull Requests.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
