"""
Test script to verify imports and dependencies.
"""
import sys
import os

def test_imports():
    """Test if all required modules can be imported."""
    print("Testing imports...")
    
    # Test standard libraries
    try:
        import logging
        print("✓ logging")
    except ImportError as e:
        print(f"✗ logging: {e}")
    
    # Test PyQt5
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        print("✓ PyQt5")
    except ImportError as e:
        print(f"✗ PyQt5: {e}")
    
    # Test moviepy
    try:
        import moviepy
        print("✓ moviepy")
    except ImportError as e:
        print(f"✗ moviepy: {e}")
    
    # Test pytube
    try:
        import pytube
        print("✓ pytube")
    except ImportError as e:
        print(f"✗ pytube: {e}")
    
    # Test yt-dlp
    try:
        import yt_dlp
        print("✓ yt_dlp")
    except ImportError as e:
        print(f"✗ yt_dlp: {e}")
    
    # Test SpeechRecognition
    try:
        import speech_recognition
        print("✓ speech_recognition")
    except ImportError as e:
        print(f"✗ speech_recognition: {e}")
    
    print("\nTest completed.")

if __name__ == "__main__":
    test_imports()
