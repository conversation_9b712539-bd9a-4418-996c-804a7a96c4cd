"""
Test script for PyQt5 GUI.
"""
import sys
from PyQt5.QtWidgets import QApp<PERSON>, QMainWindow, QPushButton, QLabel, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

class TestWindow(QMainWindow):
    """Simple test window."""

    def __init__(self):
        super().__init__()

        self.setWindowTitle("MekaVido Test")
        self.setGeometry(100, 100, 400, 300)

        # Create central widget and layout
        central_widget = QWidget()
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)

        # Add a label
        label = QLabel("MekaVido Test Application")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

        # Add a button
        button = QPushButton("Click Me")
        button.clicked.connect(self.button_clicked)
        layout.addWidget(button)

    def button_clicked(self):
        """Handle button click."""
        print("Button clicked!")

def main():
    """Main function."""
    # Enable high DPI scaling before creating QApplication
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)

    window = TestWindow()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
